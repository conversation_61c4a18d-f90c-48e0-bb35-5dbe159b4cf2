// qvxymodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qvxymodelmapper.h>
%End

    class QVXYModelMapper : QObject
    {
%TypeHeaderCode
#include <qvxymodelmapper.h>
%End

    public:
        explicit QVXYModelMapper(QObject *parent /TransferThis/ = 0);
        int xColumn() const;
        void setXColumn(int xColumn);
        int yColumn() const;
        void setYColumn(int yColumn);
        QAbstractItemModel *model() const;
        void setModel(QAbstractItemModel *model /KeepReference/);
        QtCharts::QXYSeries *series() const;
        void setSeries(QtCharts::QXYSeries *series);
        int firstRow() const;
        void setFirstRow(int firstRow);
        int rowCount() const;
        void setRowCount(int rowCount);

    signals:
        void seriesReplaced();
        void modelReplaced();
        void xColumnChanged();
        void yColumnChanged();
        void firstRowChanged();
        void rowCountChanged();
    };
};
