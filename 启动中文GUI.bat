@echo off
REM 头条内容社交工具 - 中文GUI启动脚本
REM 自动设置编码环境并启动中文GUI界面

echo 🎯 头条内容社交工具 - 中文GUI启动器
echo =======================================

REM 设置控制台编码为UTF-8
echo 📝 设置编码环境...
chcp 65001 >nul

REM 设置Python编码环境变量
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8

echo ✅ 编码环境设置完成

REM 检查虚拟环境
if not exist "venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在
    echo 请先创建虚拟环境: python -m venv venv
    pause
    exit /b 1
)

echo ✅ 虚拟环境检查通过

REM 检查关键文件
if not exist "异常监控界面.py" (
    echo ❌ 异常监控界面.py 文件不存在
    pause
    exit /b 1
)

if not exist "异常检测器.py" (
    echo ❌ 异常检测器.py 文件不存在
    pause
    exit /b 1
)

echo ✅ 关键文件检查通过

echo.
echo 🖥️ 启动中文GUI界面...
echo 💡 如果仍有编码问题，请使用英文版GUI

REM 启动GUI
venv\Scripts\python.exe 启动异常监控.py

echo.
echo 👋 GUI已关闭
pause
