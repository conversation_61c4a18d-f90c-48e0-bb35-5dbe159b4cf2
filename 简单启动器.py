#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - 简单启动器
===========================

专为虚拟环境设计的简化启动器，跳过复杂的依赖检查。

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import argparse

def 启动GUI模式():
    """启动GUI模式"""
    print("🖥️ 启动GUI界面...")
    
    try:
        from 异常监控界面 import main as gui_main
        gui_main()
        return 0
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保在虚拟环境中安装了PyQt5:")
        print("venv\\Scripts\\pip install PyQt5")
        return 1
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return 1

def 启动命令行模式(args):
    """启动命令行模式"""
    print("💻 启动命令行模式...")
    
    try:
        from 异常检测器 import 主函数 as cli_main
        
        # 重新设置sys.argv以传递参数给命令行模式
        原始argv = sys.argv.copy()
        sys.argv = ["异常检测器.py"] + args
        
        try:
            结果 = cli_main()
            return 结果
        finally:
            sys.argv = 原始argv
            
    except ImportError as e:
        print(f"❌ 导入命令行模块失败: {e}")
        print("请确保异常检测器.py文件存在")
        return 1
    except Exception as e:
        print(f"❌ 启动命令行模式失败: {e}")
        return 1

def 显示帮助():
    """显示帮助信息"""
    print("""
🔍 头条内容社交工具 - 异常监控系统 (简化版)
==========================================

使用方法:
  python 简单启动器.py                    # 启动GUI界面
  python 简单启动器.py --cli              # 启动命令行模式
  python 简单启动器.py --cli --help       # 查看命令行参数

GUI模式功能:
  ✅ 实时异常监控
  ✅ 异常统计图表
  ✅ 系统健康度仪表盘
  ✅ 异常详情查看
  ✅ 报告导出功能

命令行模式功能:
  ✅ 实时监控 (--monitor)
  ✅ 历史分析 (--analyze [天数])
  ✅ 生成报告 (--report)
  ✅ 健康度检查 (--health)
  ✅ 统计信息 (--stats [时间范围])

示例:
  python 简单启动器.py --cli --monitor           # 启动实时监控
  python 简单启动器.py --cli --analyze 7         # 分析7天历史
  python 简单启动器.py --cli --health             # 检查健康度
  python 简单启动器.py --cli --report             # 生成报告

虚拟环境使用:
  venv\\Scripts\\python.exe 简单启动器.py
""")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="异常监控系统简化启动器", add_help=False)
    parser.add_argument("--cli", action="store_true", help="启动命令行模式")
    parser.add_argument("--help", "-h", action="store_true", help="显示帮助信息")
    
    # 解析已知参数
    已知参数, 未知参数 = parser.parse_known_args()
    
    # 显示帮助
    if 已知参数.help:
        显示帮助()
        return 0
    
    print("🔍 头条内容社交工具 - 异常监控系统 (简化版)")
    print("=" * 55)
    
    # 显示环境信息
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查关键文件
    关键文件 = ["异常检测配置.json", "异常检测器.py", "异常监控界面.py"]
    缺失文件 = []
    
    for 文件 in 关键文件:
        if os.path.exists(文件):
            print(f"✅ {文件}")
        else:
            print(f"❌ {文件} (缺失)")
            缺失文件.append(文件)
    
    if 缺失文件:
        print(f"\n❌ 缺失关键文件，请检查项目完整性")
        return 1
    
    print()  # 空行
    
    # 根据参数选择启动模式
    if 已知参数.cli:
        return 启动命令行模式(未知参数)
    else:
        return 启动GUI模式()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
