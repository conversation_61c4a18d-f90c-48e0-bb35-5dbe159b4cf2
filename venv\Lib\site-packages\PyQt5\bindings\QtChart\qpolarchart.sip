// qpolarchart.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_3_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qpolarchart.h>
%End

    class QPolarChart : public QtCharts::QChart
    {
%TypeHeaderCode
#include <qpolarchart.h>
%End

    public:
        enum PolarOrientation
        {
            PolarOrientationRadial,
            PolarOrientationAngular,
        };

        typedef QFlags<QtCharts::QPolarChart::PolarOrientation> PolarOrientations;
        QPolarChart(QGraphicsItem *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
        virtual ~QPolarChart();
        void addAxis(QtCharts::QAbstractAxis *axis /Transfer/, QtCharts::QPolarChart::PolarOrientation polarOrientation);
        QList<QtCharts::QAbstractAxis*> axes(QFlags<QtCharts::QPolarChart::PolarOrientation> polarOrientation = QFlags<QtCharts::QPolarChart::PolarOrientation>(QFlag(3)), QtCharts::QAbstractSeries *series = 0) const;
        static QtCharts::QPolarChart::PolarOrientation axisPolarOrientation(QtCharts::QAbstractAxis *axis);
    };
};

%End
