#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时监控功能脚本
用于生成测试日志，验证实时监控的异常捕捉能力
"""

import os
import time
import random
from datetime import datetime

def 创建测试日志目录():
    """创建测试日志目录"""
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        print(f"✅ 创建日志目录: {logs_dir}")
    return logs_dir

def 生成测试异常日志(logs_dir):
    """生成各种类型的测试异常日志"""
    
    # 测试异常模式
    测试异常模式 = {
        "程序闪退": [
            "FATAL ERROR: Application crashed unexpectedly",
            "程序发生严重错误，即将退出",
            "Segmentation fault (core dumped)",
            "Critical: 应用程序异常终止"
        ],
        "登录失败": [
            "ERROR: Login failed - invalid credentials",
            "登录失败: Cookie已过期",
            "WARNING: 账号被禁言，无法登录",
            "Authentication error: 验证码错误"
        ],
        "网络错误": [
            "ERROR: Connection timeout after 30 seconds",
            "网络连接失败: 连接被拒绝",
            "WARNING: DNS解析失败",
            "Proxy connection error: 代理服务器无响应"
        ],
        "文件操作错误": [
            "ERROR: File not found: /path/to/file.txt",
            "权限不足: 无法写入文件",
            "WARNING: 磁盘空间不足",
            "Access denied: 文件被其他程序占用"
        ],
        "内存错误": [
            "CRITICAL: Out of memory - cannot allocate buffer",
            "内存泄漏检测: 发现未释放的内存块",
            "ERROR: Memory corruption detected",
            "Heap overflow: 堆栈溢出"
        ],
        "线程错误": [
            "ERROR: Thread synchronization failed",
            "QThread错误: 跨线程操作检测",
            "WARNING: 检测到死锁情况",
            "Thread pool exhausted: 线程池已满"
        ],
        "数据处理错误": [
            "ERROR: JSON parse error - invalid format",
            "编码错误: 无法解码UTF-8字符",
            "WARNING: 数据损坏，尝试恢复",
            "Invalid data format: 数据格式不正确"
        ]
    }
    
    # 生成不同类型的日志文件
    日志文件类型 = [
        ("app_test.log", "应用程序测试日志"),
        ("crash_log_test.txt", "崩溃日志测试"),
        ("toutiao_test.log", "头条工具测试日志")
    ]
    
    print("🔍 开始生成测试异常日志...")
    
    for 文件名, 描述 in 日志文件类型:
        文件路径 = os.path.join(logs_dir, 文件名)
        
        print(f"\n📝 生成 {描述}: {文件名}")
        
        with open(文件路径, 'w', encoding='utf-8') as f:
            # 写入文件头
            f.write(f"# {描述}\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 用于测试实时监控功能\n\n")
            
            # 随机生成异常日志
            for i in range(10):
                异常类型 = random.choice(list(测试异常模式.keys()))
                异常消息 = random.choice(测试异常模式[异常类型])
                时间戳 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                日志行 = f"[{时间戳}] {异常消息}\n"
                f.write(日志行)
                
                print(f"   • {异常类型}: {异常消息[:50]}...")
                
                # 模拟实时写入
                f.flush()
                time.sleep(0.5)
    
    print("\n✅ 测试日志生成完成")

def 模拟实时异常生成(logs_dir, 持续时间=60):
    """模拟实时异常生成"""
    print(f"\n🚀 开始模拟实时异常生成 (持续 {持续时间} 秒)...")
    
    实时异常模式 = [
        "ERROR: 用户操作异常 - 点击无效元素",
        "WARNING: 网络延迟过高，重试中...",
        "INFO: 数据同步完成",
        "ERROR: 视频处理失败 - 格式不支持",
        "CRITICAL: 内存使用率超过90%",
        "WARNING: Cookie即将过期",
        "ERROR: 浏览器驱动连接失败",
        "INFO: 账号状态检查完成",
        "ERROR: AI服务响应超时",
        "WARNING: 磁盘空间不足20%"
    ]
    
    实时日志文件 = os.path.join(logs_dir, "realtime_test.log")
    
    开始时间 = time.time()
    异常计数 = 0
    
    with open(实时日志文件, 'w', encoding='utf-8') as f:
        f.write(f"# 实时异常测试日志\n")
        f.write(f"# 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        while time.time() - 开始时间 < 持续时间:
            # 随机选择异常
            异常消息 = random.choice(实时异常模式)
            时间戳 = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            
            日志行 = f"[{时间戳}] {异常消息}\n"
            f.write(日志行)
            f.flush()
            
            异常计数 += 1
            print(f"[{异常计数:03d}] {异常消息}")
            
            # 随机间隔 1-5 秒
            time.sleep(random.uniform(1, 5))
    
    print(f"\n✅ 实时异常生成完成，共生成 {异常计数} 条异常记录")

def 生成严重问题测试():
    """生成严重问题测试日志"""
    print("\n🚨 生成严重问题测试日志...")
    
    logs_dir = "logs"
    严重问题文件 = os.path.join(logs_dir, "critical_test.log")
    
    严重问题列表 = [
        "FATAL: Application crashed - segmentation fault",
        "CRITICAL: Out of memory - system unstable",
        "ERROR: Thread deadlock detected - system frozen",
        "FATAL: 程序崩溃 - 无法恢复",
        "CRITICAL: 内存泄漏严重 - 系统即将崩溃",
        "ERROR: 线程同步失败 - 数据损坏风险"
    ]
    
    with open(严重问题文件, 'w', encoding='utf-8') as f:
        f.write(f"# 严重问题测试日志\n")
        f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for i, 问题 in enumerate(严重问题列表, 1):
            时间戳 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            日志行 = f"[{时间戳}] {问题}\n"
            f.write(日志行)
            f.flush()
            
            print(f"   {i}. {问题}")
            time.sleep(2)  # 每2秒生成一个严重问题
    
    print("✅ 严重问题测试日志生成完成")

def 清理测试日志():
    """清理测试日志"""
    确认 = input("\n是否清理测试日志文件? (y/n): ").lower().strip()
    
    if 确认 in ['y', 'yes', '是']:
        logs_dir = "logs"
        测试文件 = [
            "app_test.log",
            "crash_log_test.txt", 
            "toutiao_test.log",
            "realtime_test.log",
            "critical_test.log"
        ]
        
        清理数量 = 0
        for 文件名 in 测试文件:
            文件路径 = os.path.join(logs_dir, 文件名)
            if os.path.exists(文件路径):
                os.remove(文件路径)
                清理数量 += 1
                print(f"🗑️ 已删除: {文件名}")
        
        print(f"✅ 清理完成，删除了 {清理数量} 个测试文件")
    else:
        print("👋 跳过清理")

def 显示使用说明():
    """显示使用说明"""
    print("""
🔍 实时监控功能测试说明
========================

本脚本用于测试实时监控功能的异常捕捉能力：

📋 测试内容：
1. 生成各种类型的测试异常日志
2. 模拟实时异常生成
3. 生成严重问题测试日志

🎯 测试步骤：
1. 运行本脚本生成测试日志
2. 启动原生异常监控界面
3. 在实时监控选项卡中点击"开始监控"
4. 观察实时异常流中的异常捕捉情况

📊 预期结果：
• 实时监控应该能够捕捉到各种类型的异常
• 严重问题应该触发桌面通知
• 功能模块状态应该实时更新
• 异常统计数量应该增加

⚠️ 注意事项：
• 测试日志仅用于功能验证
• 测试完成后可以清理测试文件
• 不会影响真实的日志文件
""")

def main():
    """主函数"""
    print("🔍 实时监控功能测试工具")
    print("版本: 1.0.0")
    
    显示使用说明()
    
    try:
        # 创建日志目录
        logs_dir = 创建测试日志目录()
        
        print("\n请选择测试模式:")
        print("1. 生成基础测试日志")
        print("2. 模拟实时异常生成")
        print("3. 生成严重问题测试")
        print("4. 全部测试")
        print("5. 清理测试日志")
        
        选择 = input("\n请输入选择 (1-5): ").strip()
        
        if 选择 == "1":
            生成测试异常日志(logs_dir)
        elif 选择 == "2":
            持续时间 = int(input("请输入持续时间(秒，默认60): ") or "60")
            模拟实时异常生成(logs_dir, 持续时间)
        elif 选择 == "3":
            生成严重问题测试()
        elif 选择 == "4":
            生成测试异常日志(logs_dir)
            time.sleep(2)
            生成严重问题测试()
            time.sleep(2)
            模拟实时异常生成(logs_dir, 30)
        elif 选择 == "5":
            清理测试日志()
        else:
            print("❌ 无效选择")
            return 1
        
        if 选择 in ["1", "2", "3", "4"]:
            print("\n🎯 测试日志生成完成！")
            print("现在可以启动原生异常监控界面进行测试:")
            print("venv\\Scripts\\python.exe 启动原生异常监控.py")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
        return 0
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
