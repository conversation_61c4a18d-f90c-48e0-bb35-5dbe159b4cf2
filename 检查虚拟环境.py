#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
虚拟环境配置检查工具
==================

检查虚拟环境是否正确配置，并自动安装缺失的依赖。

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import subprocess
from pathlib import Path

def 检查Python版本():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    版本信息 = sys.version_info
    版本字符串 = f"{版本信息.major}.{版本信息.minor}.{版本信息.micro}"
    print(f"   当前版本: {版本字符串}")
    
    if 版本信息.major >= 3 and 版本信息.minor >= 7:
        print("   ✅ Python版本符合要求 (>=3.7)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7或更高版本")
        return False

def 检查虚拟环境路径():
    """检查虚拟环境路径"""
    print("\n📁 检查虚拟环境...")
    
    虚拟环境路径 = Path("venv")
    if os.name == 'nt':  # Windows
        python路径 = 虚拟环境路径 / "Scripts" / "python.exe"
        pip路径 = 虚拟环境路径 / "Scripts" / "pip.exe"
    else:  # Linux/Mac
        python路径 = 虚拟环境路径 / "bin" / "python"
        pip路径 = 虚拟环境路径 / "bin" / "pip"
    
    if 虚拟环境路径.exists():
        print(f"   ✅ 虚拟环境目录存在: {虚拟环境路径}")
    else:
        print(f"   ❌ 虚拟环境目录不存在: {虚拟环境路径}")
        return False, None, None
    
    if python路径.exists():
        print(f"   ✅ Python解释器存在: {python路径}")
    else:
        print(f"   ❌ Python解释器不存在: {python路径}")
        return False, None, None
    
    if pip路径.exists():
        print(f"   ✅ pip工具存在: {pip路径}")
    else:
        print(f"   ❌ pip工具不存在: {pip路径}")
        return False, None, None
    
    return True, str(python路径), str(pip路径)

def 检查依赖库(python路径: str):
    """检查必需的依赖库"""
    print("\n📦 检查依赖库...")
    
    必需库 = {
        "PyQt5": "GUI界面框架",
        "watchdog": "文件监控",
        "psutil": "系统信息",
        "PyQtChart": "图表组件"
    }
    
    缺失库 = []
    
    for 库名, 描述 in 必需库.items():
        try:
            result = subprocess.run([
                python路径, "-c", f"import {库名}"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ {库名} - {描述}")
            else:
                print(f"   ❌ {库名} - {描述} (未安装)")
                缺失库.append(库名)
                
        except Exception as e:
            print(f"   ❌ {库名} - {描述} (检查失败: {e})")
            缺失库.append(库名)
    
    return 缺失库

def 安装依赖库(python路径: str, 缺失库: list):
    """安装缺失的依赖库"""
    if not 缺失库:
        print("\n✅ 所有依赖库都已安装")
        return True
    
    print(f"\n📥 需要安装 {len(缺失库)} 个依赖库...")
    
    用户确认 = input("是否自动安装缺失的依赖库？(y/N): ").strip().lower()
    if 用户确认 not in ['y', 'yes', '是']:
        print("⏭️ 跳过自动安装")
        return False
    
    try:
        for 库名 in 缺失库:
            print(f"   📦 正在安装 {库名}...")
            result = subprocess.run([
                python路径, "-m", "pip", "install", 库名
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ {库名} 安装成功")
            else:
                print(f"   ❌ {库名} 安装失败: {result.stderr}")
                return False
        
        print("🎉 所有依赖库安装完成！")
        return True
        
    except Exception as e:
        print(f"❌ 安装过程中发生错误: {e}")
        return False

def 检查项目文件():
    """检查项目核心文件"""
    print("\n📄 检查项目文件...")
    
    核心文件 = [
        "异常检测配置.json",
        "异常检测器.py",
        "异常监控界面.py",
        "启动异常监控.py"
    ]
    
    缺失文件 = []
    
    for 文件名 in 核心文件:
        if os.path.exists(文件名):
            文件大小 = os.path.getsize(文件名) / 1024  # KB
            print(f"   ✅ {文件名} ({文件大小:.1f} KB)")
        else:
            print(f"   ❌ {文件名} (缺失)")
            缺失文件.append(文件名)
    
    return len(缺失文件) == 0

def 检查日志目录():
    """检查日志目录"""
    print("\n📂 检查日志目录...")
    
    日志目录 = "logs"
    if os.path.exists(日志目录):
        日志文件 = [f for f in os.listdir(日志目录) if f.endswith(('.log', '.txt'))]
        print(f"   ✅ 日志目录存在: {日志目录}")
        print(f"   📊 日志文件数量: {len(日志文件)}")
        
        # 统计不同类型的日志
        app_logs = len([f for f in 日志文件 if f.startswith('app_')])
        crash_logs = len([f for f in 日志文件 if f.startswith('crash_log_')])
        toutiao_logs = len([f for f in 日志文件 if f.startswith('toutiao_')])
        
        print(f"   📱 应用日志: {app_logs} 个")
        print(f"   💥 崩溃日志: {crash_logs} 个")
        print(f"   🎯 头条日志: {toutiao_logs} 个")
        
        return True
    else:
        print(f"   ⚠️ 日志目录不存在: {日志目录}")
        print("   这是正常的，系统运行时会自动创建")
        return True

def 运行快速测试(python路径: str):
    """运行快速功能测试"""
    print("\n🧪 运行快速功能测试...")
    
    用户确认 = input("是否运行快速功能测试？(y/N): ").strip().lower()
    if 用户确认 not in ['y', 'yes', '是']:
        print("⏭️ 跳过功能测试")
        return True
    
    try:
        print("   🔍 测试异常检测器导入...")
        result = subprocess.run([
            python路径, "-c", 
            "from 异常检测器 import 异常检测器主类; print('✅ 导入成功')"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 异常检测器导入测试通过")
        else:
            print(f"   ❌ 异常检测器导入测试失败: {result.stderr}")
            return False
        
        print("   🏥 测试健康度检查...")
        result = subprocess.run([
            python路径, "异常检测器.py", "--health"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("   ✅ 健康度检查测试通过")
            # 提取关键信息
            输出行 = result.stdout.split('\n')
            for 行 in 输出行:
                if "系统健康度评分" in 行:
                    print(f"   📊 {行.strip()}")
        else:
            print(f"   ❌ 健康度检查测试失败: {result.stderr}")
            return False
        
        print("🎉 快速功能测试通过！")
        return True
        
    except subprocess.TimeoutExpired:
        print("   ⏰ 测试超时")
        return False
    except Exception as e:
        print(f"   ❌ 测试过程中发生错误: {e}")
        return False

def 生成使用指南():
    """生成使用指南"""
    print("\n📚 使用指南:")
    print("=" * 50)
    print("🚀 启动方式:")
    print("   1. GUI界面:")
    print("      venv\\Scripts\\python.exe 启动异常监控.py")
    print("   2. 命令行:")
    print("      venv\\Scripts\\python.exe 异常检测器.py --help")
    print("   3. 虚拟环境启动器:")
    print("      venv\\Scripts\\python.exe 虚拟环境启动器.py")
    print("")
    print("🔧 常用命令:")
    print("   • 健康度检查: venv\\Scripts\\python.exe 异常检测器.py --health")
    print("   • 生成报告: venv\\Scripts\\python.exe 异常检测器.py --report")
    print("   • 实时监控: venv\\Scripts\\python.exe 异常检测器.py --monitor")
    print("")
    print("📁 重要文件:")
    print("   • 配置文件: 异常检测配置.json")
    print("   • 使用说明: 异常检测系统使用说明.md")
    print("   • 项目总结: 项目完成总结.md")

def 主程序():
    """主程序"""
    print("🔧 虚拟环境配置检查工具")
    print("=" * 50)
    
    检查结果 = []
    
    # 1. 检查Python版本
    结果 = 检查Python版本()
    检查结果.append(("Python版本", 结果))
    
    # 2. 检查虚拟环境
    结果, python路径, pip路径 = 检查虚拟环境路径()
    检查结果.append(("虚拟环境", 结果))
    
    if not 结果:
        print("\n❌ 虚拟环境检查失败，请先创建虚拟环境")
        print("创建命令: python -m venv venv")
        return 1
    
    # 3. 检查依赖库
    缺失库 = 检查依赖库(python路径)
    依赖库完整 = len(缺失库) == 0
    检查结果.append(("依赖库", 依赖库完整))
    
    # 4. 安装缺失依赖
    if 缺失库:
        安装成功 = 安装依赖库(python路径, 缺失库)
        if 安装成功:
            依赖库完整 = True
            检查结果[-1] = ("依赖库", True)
    
    # 5. 检查项目文件
    结果 = 检查项目文件()
    检查结果.append(("项目文件", 结果))
    
    # 6. 检查日志目录
    结果 = 检查日志目录()
    检查结果.append(("日志目录", 结果))
    
    # 7. 运行功能测试
    if 依赖库完整:
        结果 = 运行快速测试(python路径)
        检查结果.append(("功能测试", 结果))
    
    # 显示检查结果
    print("\n" + "=" * 50)
    print("📋 检查结果汇总:")
    
    成功数 = 0
    总数 = len(检查结果)
    
    for 项目, 结果 in 检查结果:
        状态 = "✅ 通过" if 结果 else "❌ 失败"
        print(f"   {项目}: {状态}")
        if 结果:
            成功数 += 1
    
    print(f"\n📊 总体结果: {成功数}/{总数} 项检查通过")
    
    if 成功数 == 总数:
        print("🎉 虚拟环境配置完美！系统可以正常使用。")
        生成使用指南()
        return 0
    elif 成功数 >= 总数 * 0.8:
        print("⚠️ 大部分配置正常，少数问题需要处理。")
        生成使用指南()
        return 0
    else:
        print("❌ 配置存在较多问题，请根据提示进行修复。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = 主程序()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断检查")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 检查过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
