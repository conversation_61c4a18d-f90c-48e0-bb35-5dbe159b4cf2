// qareaseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qareaseries.h>
%End

    class QAreaSeries : public QtCharts::QAbstractSeries
    {
%TypeHeaderCode
#include <qareaseries.h>
%End

    public:
        explicit QAreaSeries(QObject *parent /TransferThis/ = 0);
        QAreaSeries(QtCharts::QLineSeries *upperSeries, QtCharts::QLineSeries *lowerSeries = 0);
        virtual ~QAreaSeries();
        virtual QtCharts::QAbstractSeries::SeriesType type() const;
        void setUpperSeries(QtCharts::QLineSeries *series);
        QtCharts::QLineSeries *upperSeries() const;
        void setLowerSeries(QtCharts::QLineSeries *series);
        QtCharts::QLineSeries *lowerSeries() const;
        void setPen(const QPen &pen);
        QPen pen() const;
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setPointsVisible(bool visible = true);
        bool pointsVisible() const;
        void setColor(const QColor &color);
        QColor color() const;
        void setBorderColor(const QColor &color);
        QColor borderColor() const;

    signals:
        void borderColorChanged(QColor color);
        void colorChanged(QColor color);
        void clicked(const QPointF &point);
        void selected();
%If (QtChart_1_2_0 -)
        void hovered(const QPointF &point, bool state);
%End

    public:
%If (QtChart_1_4_0 -)
        void setPointLabelsFormat(const QString &format);
%End
%If (QtChart_1_4_0 -)
        QString pointLabelsFormat() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsVisible(bool visible = true);
%End
%If (QtChart_1_4_0 -)
        bool pointLabelsVisible() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsFont(const QFont &font);
%End
%If (QtChart_1_4_0 -)
        QFont pointLabelsFont() const;
%End
%If (QtChart_1_4_0 -)
        void setPointLabelsColor(const QColor &color);
%End
%If (QtChart_1_4_0 -)
        QColor pointLabelsColor() const;
%End

    signals:
%If (QtChart_1_4_0 -)
        void pointLabelsFormatChanged(const QString &format);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsVisibilityChanged(bool visible);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsFontChanged(const QFont &font);
%End
%If (QtChart_1_4_0 -)
        void pointLabelsColorChanged(const QColor &color);
%End
%If (QtChart_2_0_0 -)
        void pressed(const QPointF &point);
%End
%If (QtChart_2_0_0 -)
        void released(const QPointF &point);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(const QPointF &point);
%End

    public:
%If (QtChart_2_1_0 -)
        void setPointLabelsClipping(bool enable = true);
%End
%If (QtChart_2_1_0 -)
        bool pointLabelsClipping() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void pointLabelsClippingChanged(bool clipping);
%End
    };
};
