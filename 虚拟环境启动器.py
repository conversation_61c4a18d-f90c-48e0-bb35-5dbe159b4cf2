#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - 异常监控系统虚拟环境启动器
==============================================

专门为虚拟环境设计的启动脚本，自动检测和使用虚拟环境中的Python解释器。

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import subprocess
from pathlib import Path

def 检查虚拟环境():
    """检查虚拟环境是否存在"""
    虚拟环境路径 = Path("venv")
    
    if os.name == 'nt':  # Windows
        python路径 = 虚拟环境路径 / "Scripts" / "python.exe"
    else:  # Linux/Mac
        python路径 = 虚拟环境路径 / "bin" / "python"
    
    if python路径.exists():
        return str(python路径)
    else:
        return None

def 显示菜单():
    """显示主菜单"""
    print("\n🔍 头条内容社交工具 - 异常监控系统")
    print("=" * 50)
    print("请选择启动模式:")
    print("[1] 🖥️  GUI界面模式")
    print("[2] 🏥  健康度检查")
    print("[3] 📝  生成分析报告")
    print("[4] 🔍  实时监控")
    print("[5] 📊  查看统计信息")
    print("[6] 🧪  运行测试")
    print("[7] 📚  查看帮助")
    print("[0] 🚪  退出")
    print("-" * 50)

def 执行命令(python路径: str, 命令: list):
    """执行命令"""
    try:
        完整命令 = [python路径] + 命令
        result = subprocess.run(完整命令, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        return True

def 主程序():
    """主程序"""
    print("🚀 异常监控系统虚拟环境启动器")
    
    # 检查虚拟环境
    python路径 = 检查虚拟环境()
    if not python路径:
        print("❌ 未找到虚拟环境！")
        print("请先创建虚拟环境:")
        print("   python -m venv venv")
        print("然后安装依赖:")
        print("   venv\\Scripts\\pip install PyQt5 watchdog psutil PyQtChart")
        input("\n按回车键退出...")
        return 1
    
    print(f"✅ 找到虚拟环境: {python路径}")
    
    # 主循环
    while True:
        显示菜单()
        
        try:
            选择 = input("请输入选择 (0-7): ").strip()
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        
        if 选择 == "0":
            print("👋 感谢使用异常监控系统！")
            break
        elif 选择 == "1":
            print("\n🖥️ 启动GUI界面...")
            执行命令(python路径, ["启动异常监控.py"])
        elif 选择 == "2":
            print("\n🏥 执行健康度检查...")
            执行命令(python路径, ["异常检测器.py", "--health"])
            input("\n按回车键继续...")
        elif 选择 == "3":
            print("\n📝 生成分析报告...")
            执行命令(python路径, ["异常检测器.py", "--report", "--format", "HTML", "JSON", "CSV"])
            input("\n按回车键继续...")
        elif 选择 == "4":
            print("\n🔍 启动实时监控 (按Ctrl+C停止)...")
            执行命令(python路径, ["异常检测器.py", "--monitor"])
            input("\n按回车键继续...")
        elif 选择 == "5":
            print("\n📊 查看统计信息...")
            时间范围 = input("请输入时间范围 (1小时/24小时/7天/30天，默认24小时): ").strip()
            if not 时间范围:
                时间范围 = "24小时"
            执行命令(python路径, ["异常检测器.py", "--stats", 时间范围])
            input("\n按回车键继续...")
        elif 选择 == "6":
            print("\n🧪 运行系统测试...")
            执行命令(python路径, ["直接测试.py"])
            input("\n按回车键继续...")
        elif 选择 == "7":
            print("\n📚 异常检测器帮助信息:")
            执行命令(python路径, ["异常检测器.py", "--help"])
            input("\n按回车键继续...")
        else:
            print("❌ 无效选择，请重新输入")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = 主程序()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
