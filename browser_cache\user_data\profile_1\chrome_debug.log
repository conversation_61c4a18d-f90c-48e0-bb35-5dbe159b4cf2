[49804:48856:0801/050837.745:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[49804:48856:0801/050837.818:WARNING:chrome\browser\chrome_browser_main_win.cc:863] Command line too long for RegisterApplicationRestart:  --allow-pre-commit-input --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-blink-features=AutomationControlled --disable-client-side-phishing-detection --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=VizDisplayCompositor --disable-gpu --disable-hang-monitor --disable-images --disable-ipc-flooding-protection --disable-plugins --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --disable-sync --disable-translate --disable-web-security --disk-cache-dir="e:\toutiaoyuanma1\tou014\tou011\browser_cache\disk_cache" --disk-cache-size=104857600 --enable-logging --headless --log-level=0 --media-cache-dir="e:\toutiaoyuanma1\tou014\tou011\browser_cache\media_cache" --media-cache-size=52428800 --metrics-recording-only --no-default-browser-check --no-first-run --no-report-upload --no-sandbox --no-service-autorun --noerrdialogs --password-store=basic --remote-debugging-port=0 --test-type=webdriver --use-mock-keychain --user-data-dir="e:\toutiaoyuanma1\tou014\tou011\browser_cache\user_data\profile_1" --restore-last-session --restart
[31996:800:0801/050838.249:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[55244:53616:0801/050838.342:VERBOSE1:components\viz\service\main\viz_main_impl.cc:86] VizNullHypothesis is disabled (not a warning)
[50580:57256:0801/050838.625:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[6596:27408:0801/050838.670:ERROR:net\disk_cache\cache_util_win.cc:20] Unable to move the cache: 拒绝访问。 (0x5)
[6596:10344:0801/050838.673:ERROR:net\disk_cache\disk_cache.cc:216] Unable to create cache
[36100:17924:0801/050838.773:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[47228:55288:0801/050838.972:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[49804:48856:0801/050840.359:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/050840.533:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050840.840:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6596:10344:0801/050842.397:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 59
[6596:10344:0801/050842.399:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 1
[55244:53616:0801/050842.455:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050842.606:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[49804:48856:0801/050842.951:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/050843.495:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050843.539:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6596:10344:0801/050843.666:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050843.699:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050843.712:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050843.713:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050843.714:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050845.450:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050845.465:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[55244:53616:0801/050845.480:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050845.521:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6596:10344:0801/050845.890:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 165
[55244:53616:0801/050845.986:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050846.026:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050846.159:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/050846.701:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050846.714:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050846.941:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050846.950:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050846.960:ERROR:gpu\command_buffer\service\gl_utils.cc:443] [.WebGL-0x50840ae04e00]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[55244:53616:0801/050846.989:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050846.997:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050847.008:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050847.011:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050847.012:INFO:CONSOLE:0] "[.WebGL-0x50840ae04e00]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050847.013:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050847.014:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050848.669:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050848.701:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050848.716:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050848.718:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050848.718:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050848.981:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050848.988:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050849.001:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050849.011:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050850.952:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050852.820:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050854.451:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050854.461:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050854.492:ERROR:gpu\command_buffer\service\gl_utils.cc:443] [.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[6596:10344:0801/050854.561:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050854.581:ERROR:gpu\command_buffer\service\gl_utils.cc:443] [.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[55244:53616:0801/050854.662:ERROR:gpu\command_buffer\service\gl_utils.cc:443] [.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels (this message will no longer repeat)
[49804:48856:0801/050854.788:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050854.791:INFO:CONSOLE:0] "[.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050854.792:INFO:CONSOLE:0] "[.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050854.794:INFO:CONSOLE:0] "[.WebGL-0x508400131500]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels (this message will no longer repeat)", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050855.935:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050856.526:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050857.824:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050859.568:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050900.945:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050901.529:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050906.795:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050907.267:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050911.798:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050912.269:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.819:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.851:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.890:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.895:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.898:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.901:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.904:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.919:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050915.927:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050915.931:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050915.937:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050915.941:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/050917.814:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[6596:10344:0801/050918.707:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050919.216:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050919.223:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050919.250:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050919.256:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050919.320:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050919.320:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050919.320:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050919.320:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050919.363:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050919.432:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050919.506:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050919.513:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050920.352:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/050920.857:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050922.316:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050922.324:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050922.343:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050922.357:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050923.710:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050924.657:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050925.355:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050925.872:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050927.292:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050927.301:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050927.565:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050927.567:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050929.659:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050930.882:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050931.639:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050933.582:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050936.643:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050937.588:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050938.587:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050942.591:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.423:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.425:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.426:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.427:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.428:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.429:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.431:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.431:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.431:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.432:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.432:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.434:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/050944.436:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050944.439:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050946.257:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050946.486:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050946.535:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0902B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (3)
[49804:48856:0801/050946.669:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/050947.376:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050947.387:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050947.416:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050947.422:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050947.435:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050947.435:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050947.435:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050947.435:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050947.516:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050947.614:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050949.020:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050949.477:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050949.488:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/050949.504:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/050949.514:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/050949.944:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050951.205:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050951.259:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/050953.528:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/050953.537:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[6596:10344:0801/050953.756:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/050953.856:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/050954.022:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050954.930:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050956.211:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050958.764:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/050959.940:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051001.093:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051004.043:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051006.104:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051009.052:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051011.758:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051016.763:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051029.043:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051029.044:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051031.230:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051034.046:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051034.046:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051036.237:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051043.336:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051044.086:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.570:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.571:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.571:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.572:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.572:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.573:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051046.575:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.576:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.577:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.577:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.578:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.578:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.582:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051046.582:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051048.340:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051048.549:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051048.624:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051048.661:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051049.275:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051049.923:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051049.966:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051049.973:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051049.997:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051050.004:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051050.015:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051050.054:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051050.054:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051050.054:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051050.054:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0002B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051051.121:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051051.624:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051051.630:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0302B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051051.649:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051051.656:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0602B000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051051.842:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051054.290:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051054.606:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051054.614:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051054.876:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051054.877:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051056.125:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051056.883:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051101.889:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051109.058:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051109.059:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051109.060:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051111.132:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051114.068:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051114.069:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051114.069:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051116.154:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051124.093:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051127.087:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051129.098:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051129.551:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.555:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.557:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.558:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.560:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.561:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.564:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051129.566:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051129.568:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051131.135:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051131.137:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051131.546:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051131.612:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (3)
[49804:48856:0801/051131.724:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[6596:10344:0801/051132.095:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051132.842:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051132.849:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051132.880:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051132.888:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051133.461:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051133.461:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051133.464:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051133.464:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051133.528:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051133.534:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051134.160:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051134.527:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051134.535:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051134.545:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051134.553:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051134.803:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051135.204:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051136.146:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051137.507:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051137.516:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051137.792:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051137.792:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051139.172:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051139.845:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051140.208:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051144.851:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051149.069:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051149.070:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051149.072:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051154.074:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051154.074:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051154.074:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051204.113:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051204.219:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.223:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.225:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.226:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.230:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.232:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.241:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.244:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.245:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051204.246:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051204.251:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051205.935:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051206.011:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051206.048:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051206.623:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051206.635:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051206.677:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051206.689:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051206.767:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051206.767:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051206.769:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051206.769:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051207.616:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051207.691:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051208.829:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051209.010:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051209.017:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051209.029:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051209.036:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051209.431:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051211.162:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051211.164:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051212.249:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051212.255:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051212.491:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051213.831:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051214.186:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051214.449:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051216.172:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051216.172:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051219.188:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051219.463:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051219.861:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051220.603:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051224.872:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051225.608:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051229.080:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051234.085:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.626:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.627:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.629:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.630:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.632:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.633:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051235.635:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.636:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.636:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.637:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.638:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.639:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.646:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.646:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.646:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.648:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051235.650:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[55244:53616:0801/051236.853:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051236.915:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (3)
[49804:48856:0801/051237.014:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051237.736:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051237.745:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051237.770:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051237.777:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051237.806:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051237.806:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0202F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051237.806:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0502F000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051237.806:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051238.457:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051238.505:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051239.828:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051239.847:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051239.854:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051239.865:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051239.872:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051240.149:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051242.951:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051242.960:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051243.235:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051244.136:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051244.830:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051245.183:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051248.834:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051248.847:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051249.139:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051250.192:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051251.162:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051251.192:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051253.839:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051253.852:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051256.167:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051256.196:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051319.842:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051324.143:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051324.145:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051324.844:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051325.219:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051328.854:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051328.868:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051329.146:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051329.148:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051330.227:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051331.183:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051333.857:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051333.873:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051336.186:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051356.946:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.950:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.951:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.952:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.954:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.955:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.959:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.962:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.964:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.964:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.966:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[6596:10344:0801/051356.966:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -103
[49804:48856:0801/051358.665:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051359.008:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051359.059:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051359.448:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051359.595:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051359.624:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051359.632:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051359.656:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051359.664:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051359.684:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051359.684:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051359.685:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051359.685:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051359.858:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051401.489:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051401.492:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051401.978:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051401.994:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051402.006:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051402.016:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051404.146:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051404.153:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051404.312:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051404.319:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051404.556:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051404.556:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051404.860:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051405.245:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051406.494:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051406.534:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051409.149:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051409.156:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051409.865:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051410.250:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051411.539:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051414.874:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051426.666:INFO:CONSOLE:47] "清除缓存失败: [object DOMException]", source:  (47)
[55244:53616:0801/051428.109:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051428.158:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[49804:48856:0801/051428.270:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051428.884:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051428.930:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[49804:48856:0801/051429.044:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[6596:10344:0801/051429.894:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 399
[6596:10344:0801/051429.909:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 417
[6596:10344:0801/051429.909:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 419
[6596:10344:0801/051429.917:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 413
[6596:10344:0801/051429.922:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 403
[6596:10344:0801/051429.953:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 397
[6596:10344:0801/051429.976:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 415
[49804:48856:0801/051430.515:INFO:CONSOLE:367] "[options] [object Object]", source: https://lf6-cdn2-tos.bytegoofy.com/pgcfe/mp/pages/login.908fb3ce.js (367)
[49804:48856:0801/051430.550:INFO:CONSOLE:1] "[default] appid: 1231, userInfo:{"user_unique_id":"be_null","web_id":"7533355919045215798"}", source: https://lf6-cdn2-tos.bytegoofy.com/pgcfe/mp/pages/login.908fb3ce.js (1)
[49804:48856:0801/051430.550:INFO:CONSOLE:1] "[default] appid: 1231, sdk is ready, version is 5.0.23_3, you can report now !!!", source: https://lf6-cdn2-tos.bytegoofy.com/pgcfe/mp/pages/login.908fb3ce.js (1)
[49804:48856:0801/051430.550:INFO:CONSOLE:367] "[fpStr] ===> verify_mdrw7mdk_DuRDlNZx_teJ1_4scP_94XT_o51M3CUt9qfL", source: https://lf6-cdn2-tos.bytegoofy.com/pgcfe/mp/pages/login.908fb3ce.js (367)
[6596:10344:0801/051431.365:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051431.606:INFO:CONSOLE:0] "<meta name="apple-mobile-web-app-capable" content="yes"> is deprecated. Please include <meta name="mobile-web-app-capable" content="yes">", source: https://mp.toutiao.com/auth/page/login?redirect_url=JTJGcHJvZmlsZV92NCUyRmluZGV4 (0)
[49804:48856:0801/051431.719:INFO:CONSOLE:47] "清除缓存失败: [object DOMException]", source:  (47)
[49804:48856:0801/051433.318:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051433.369:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051433.403:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[55244:53616:0801/051434.067:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051434.082:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[55244:53616:0801/051434.224:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051434.269:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[49804:48856:0801/051434.362:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051434.769:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051434.789:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6596:10344:0801/051435.370:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051435.621:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051436.368:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051437.201:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051437.209:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[55244:53616:0801/051437.220:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051437.312:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051437.362:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (3)
[49804:48856:0801/051437.454:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[55244:53616:0801/051438.088:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051438.119:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051438.767:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051438.773:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051438.801:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051438.808:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051438.843:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051438.843:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[49804:48856:0801/051438.843:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051439.876:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051440.295:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051440.301:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A08035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[55244:53616:0801/051440.312:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051440.319:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051440.377:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051440.627:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051441.503:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051441.505:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:48856:0801/051441.720:INFO:CONSOLE:0] "Uncaught (in promise) NotAllowedError: play() failed because the user didn't interact with the document first. https://goo.gl/xX8pDD", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051444.169:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[55244:53616:0801/051444.461:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A01036000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[55244:53616:0801/051444.470:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[49804:48856:0801/051444.722:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B035000C400000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/xigua/upload-video (0)
[6596:10344:0801/051444.880:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051445.260:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051446.510:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051446.510:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051446.762:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051449.172:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051450.262:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051451.770:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051506.254:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051511.257:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051515.645:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051515.646:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051519.891:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051520.650:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051521.500:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051521.531:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051524.191:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051524.901:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051526.517:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051526.533:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051529.194:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051541.894:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051546.897:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051552.290:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051555.664:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051555.666:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051557.296:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051600.676:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051600.677:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051601.544:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051601.545:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051606.548:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051606.548:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051623.082:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051628.086:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051635.676:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051635.677:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051635.691:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051635.693:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051640.690:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051640.690:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051640.704:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051640.705:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051655.300:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051700.305:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051707.857:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051712.861:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051715.684:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051715.698:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051720.687:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051720.701:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[6596:10344:0801/051758.318:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:39048:0801/051802.733:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=46})
[6596:10344:0801/051803.093:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051803.179:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051803.324:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:39048:0801/051803.733:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=45})
[49804:39048:0801/051804.741:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=43})
[6596:10344:0801/051804.834:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051805.078:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051805.749:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=45})
[6596:10344:0801/051806.193:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051806.379:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051806.757:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=46})
[49804:39048:0801/051807.768:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=49})
[49804:39048:0801/051808.780:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[6596:10344:0801/051809.590:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051809.675:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051809.787:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=52})
[49804:39048:0801/051810.800:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/051812.813:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
[6596:10344:0801/051813.087:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051813.294:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051814.440:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051814.665:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051814.824:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[49804:39048:0801/051815.831:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[49804:39048:0801/051816.837:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=61})
[49804:39048:0801/051817.846:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[49804:39048:0801/051819.872:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[49804:39048:0801/051820.880:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[6596:10344:0801/051821.311:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051821.519:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051821.885:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=63})
[6596:10344:0801/051822.632:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051822.816:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051824.906:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[49804:39048:0801/051825.912:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[49804:39048:0801/051826.920:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[6596:10344:0801/051829.527:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051829.752:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051829.937:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=67})
[6596:10344:0801/051830.867:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051830.941:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[6596:10344:0801/051831.045:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051832.966:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[6596:10344:0801/051833.089:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051833.172:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051835.991:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=61})
[49804:39048:0801/051836.999:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[6596:10344:0801/051837.761:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051837.978:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051838.005:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=57})
[49804:39048:0801/051839.011:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[6596:10344:0801/051839.089:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051839.306:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051839.574:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/bcs/notice/boxes/?app_id=1231
[6596:10344:0801/051839.575:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/api/msg/v1/list/?aid=13&app_name=news_article&from=pgc_important
[6596:10344:0801/051839.605:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051839.681:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051840.015:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[49804:39048:0801/051841.023:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=50})
[49804:39048:0801/051842.029:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=49})
[49804:39048:0801/051843.038:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=52})
[6596:10344:0801/051843.314:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051843.515:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051844.044:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=50})
[49804:39048:0801/051845.054:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[6596:10344:0801/051845.989:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051846.066:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[6596:10344:0801/051846.201:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051847.066:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[6596:10344:0801/051847.312:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051847.489:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051848.076:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/051849.086:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[49804:39048:0801/051850.094:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[49804:39048:0801/051851.103:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[49804:39048:0801/051852.103:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/051853.108:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[49804:39048:0801/051854.113:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=56})
[6596:10344:0801/051854.218:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051854.692:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051855.118:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=57})
[6596:10344:0801/051855.801:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051855.981:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051856.123:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[49804:39048:0801/051857.124:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=56})
[49804:39048:0801/051858.128:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=57})
[49804:39048:0801/051859.132:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=61})
[49804:39048:0801/051902.160:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[6596:10344:0801/051902.708:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051902.913:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051903.087:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051903.163:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=57})
[6596:10344:0801/051903.170:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051904.031:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051904.167:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
[6596:10344:0801/051904.214:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051906.180:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[49804:39048:0801/051908.204:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[49804:39048:0801/051909.209:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[6596:10344:0801/051909.611:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051909.684:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051910.220:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[6596:10344:0801/051910.926:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051911.141:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051912.239:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=65})
[6596:10344:0801/051912.254:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051912.687:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051913.244:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=67})
[49804:39048:0801/051914.246:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[49804:39048:0801/051917.258:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=67})
[49804:39048:0801/051918.267:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[6596:10344:0801/051919.154:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051919.277:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=71})
[6596:10344:0801/051919.395:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051920.280:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=70})
[6596:10344:0801/051920.509:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051920.691:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051922.293:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[49804:39048:0801/051923.299:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[49804:39048:0801/051924.306:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=68})
[49804:39048:0801/051925.315:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=64})
[49804:39048:0801/051926.325:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=61})
[49804:39048:0801/051927.336:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=60})
[6596:10344:0801/051927.414:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051927.619:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051928.350:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[6596:10344:0801/051928.750:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051928.974:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051929.354:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=50})
[49804:39048:0801/051930.358:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[49804:39048:0801/051931.365:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=52})
[49804:39048:0801/051932.369:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=49})
[6596:10344:0801/051933.086:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051933.377:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=50})
[6596:10344:0801/051933.393:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051934.382:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=46})
[49804:39048:0801/051935.394:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=48})
[6596:10344:0801/051935.624:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051936.069:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051936.404:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=50})
[6596:10344:0801/051937.180:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051937.297:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mssdk.bytedance.com/web/common?ms_appid=1231&msToken=AVNhMePib95OKlFz4y0FnWMwkIaySFI5WZI5i0P7_EOWRNPE08SshvRunJah-mUFizIEX2e0UXGv4HzeabhJU_N2WM82vrScBBmVIGZqluXo9ik65CTrvdN7dC55a76WBM9CKWz24XXcwk3cqTTSkR5U-H7CFeT7T6LJWob8N90zb7933O7XDk78ew%3D%3D
[6596:10344:0801/051937.383:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051937.407:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=52})
[49804:39048:0801/051938.411:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[49804:39048:0801/051939.415:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
[6596:10344:0801/051939.477:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mssdk.bytedance.com/web/common?ms_appid=1231&msToken=AVNhMePib95OKlFz4y0FnWMwkIaySFI5WZI5i0P7_EOWRNPE08SshvRunJah-mUFizIEX2e0UXGv4HzeabhJU_N2WM82vrScBBmVIGZqluXo9ik65CTrvdN7dC55a76WBM9CKWz24XXcwk3cqTTSkR5U-H7CFeT7T6LJWob8N90zb7933O7XDk78ew%3D%3D
[6596:10344:0801/051939.584:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/bcs/notice/boxes/?app_id=1231
[6596:10344:0801/051939.585:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/api/msg/v1/list/?aid=13&app_name=news_article&from=pgc_important
[6596:10344:0801/051939.585:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/general/wenda/invite_count
[6596:10344:0801/051939.613:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/051939.696:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/051940.421:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[6596:10344:0801/051940.982:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051941.207:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051941.427:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/051942.434:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
[49804:39048:0801/051943.442:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[6596:10344:0801/051944.084:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051944.330:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051944.448:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[6596:10344:0801/051945.444:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051945.459:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=64})
[6596:10344:0801/051945.633:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/051946.467:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=65})
[49804:39048:0801/051947.475:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=64})
[49804:39048:0801/051949.494:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=65})
[49804:39048:0801/051950.497:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=68})
[49804:39048:0801/051951.505:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=70})
[6596:10344:0801/051952.343:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/051952.506:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[6596:10344:0801/051952.539:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/051953.652:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051953.830:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/051954.073:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://optimizationguide-pa.googleapis.com/v1:GetModels?key=AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE
[49804:39048:0801/051954.529:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=68})
[49804:39048:0801/051956.536:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[6596:10344:0801/051959.086:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:39048:0801/051959.561:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=71})
[6596:10344:0801/052000.544:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052000.573:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[6596:10344:0801/052000.763:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052001.584:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=67})
[6596:10344:0801/052001.889:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052002.089:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052003.099:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052003.185:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/052003.605:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[6596:10344:0801/052004.090:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[49804:39048:0801/052005.620:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[49804:39048:0801/052006.622:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[49804:39048:0801/052007.626:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[49804:39048:0801/052008.636:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=52})
[6596:10344:0801/052008.777:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052008.988:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052009.631:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/052009.645:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=47})
[6596:10344:0801/052009.939:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052010.105:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052010.303:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052010.740:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=45})
[49804:39048:0801/052011.842:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=41})
[49804:39048:0801/052012.844:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=37})
[49804:39048:0801/052013.851:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=36})
[49804:39048:0801/052014.854:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=34})
[6596:10344:0801/052017.005:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052017.216:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052018.338:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052018.523:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052025.242:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052025.464:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052026.591:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052026.807:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052033.089:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052033.184:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052033.484:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052033.699:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052034.808:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052034.977:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052039.582:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/bcs/notice/boxes/?app_id=1231
[6596:10344:0801/052039.584:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/api/msg/v1/list/?aid=13&app_name=news_article&from=pgc_important
[6596:10344:0801/052039.623:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052039.709:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052040.994:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052041.187:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052041.715:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052041.952:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052042.507:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=35})
[6596:10344:0801/052043.070:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052043.242:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052043.513:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=37})
[49804:39048:0801/052044.519:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=41})
[49804:39048:0801/052045.528:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=44})
[49804:39048:0801/052046.536:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=48})
[49804:39048:0801/052047.549:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=51})
[49804:39048:0801/052048.560:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=55})
[49804:39048:0801/052049.562:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[6596:10344:0801/052049.962:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052050.172:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052050.564:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=64})
[6596:10344:0801/052051.296:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052051.505:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052051.566:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[49804:39048:0801/052052.574:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=68})
[49804:39048:0801/052053.578:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=69})
[49804:39048:0801/052056.603:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=67})
[49804:39048:0801/052057.616:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=66})
[6596:10344:0801/052058.183:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052058.402:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052058.623:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=62})
[6596:10344:0801/052059.531:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052059.638:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=61})
[6596:10344:0801/052059.721:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052100.650:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
[49804:39048:0801/052101.660:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=59})
[49804:39048:0801/052102.671:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=56})
[6596:10344:0801/052103.100:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052103.178:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/052103.680:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/052105.690:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[6596:10344:0801/052106.411:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[6596:10344:0801/052106.624:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/mp/agw/feedback/get_unread_feedback?UserID=564485736632587&app_id=1231
[49804:39048:0801/052106.700:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=54})
[49804:39048:0801/052107.707:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=53})
[6596:10344:0801/052107.753:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[6596:10344:0801/052107.942:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mp.toutiao.com/monitor_browser/collect/batch/?biz_id=toutiao_mp
[49804:39048:0801/052108.719:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=56})
[6596:10344:0801/052109.629:VERBOSE1:net\base\network_delegate.cc:37] NetworkDelegate::NotifyBeforeURLRequest: https://mon.zijieapi.com/monitor_browser/collect/batch/
[6596:10344:0801/052109.716:VERBOSE1:services\network\url_loader.cc:1281] Will sniff content for mime type: https://mon.zijieapi.com/monitor_browser/collect/batch/
[49804:39048:0801/052109.729:VERBOSE1:content\browser\renderer_host\media\media_stream_manager.cc:1510] MSPL::OnSpeedLimitChange({this=0x11bc00108048}, {new_limit=58})
