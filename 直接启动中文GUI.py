#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - 中文GUI直接启动器
==================================

直接启动中文GUI界面，处理所有编码问题。

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os

def 设置编码环境():
    """设置完整的编码环境"""
    try:
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'zh_CN.UTF-8'
        os.environ['LC_ALL'] = 'zh_CN.UTF-8'
        
        # 设置locale
        import locale
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, '')
                except:
                    pass
        
        print("✅ 编码环境设置完成")
        return True
        
    except Exception as e:
        print(f"⚠️ 编码环境设置失败: {e}")
        return False

def 启动中文GUI():
    """启动中文GUI界面"""
    try:
        print("🖥️ 正在启动中文GUI界面...")
        
        # 导入PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTextCodec
        
        # 设置Qt的文本编码
        try:
            QTextCodec.setCodecForLocale(QTextCodec.codecForName("UTF-8"))
        except:
            pass
        
        # 创建安全的argv
        safe_argv = []
        for i, arg in enumerate(sys.argv):
            try:
                # 尝试编码为ASCII
                arg.encode('ascii')
                safe_argv.append(arg)
            except UnicodeEncodeError:
                # 如果包含非ASCII字符，使用索引作为替代
                safe_argv.append(f"arg{i}")
        
        # 创建QApplication
        app = QApplication(safe_argv)
        app.setApplicationName("异常监控系统")
        app.setApplicationVersion("1.0.0")
        
        print("✅ Qt应用创建成功")
        
        # 导入并创建主窗口
        from 异常监控界面 import 异常监控主窗口
        
        主窗口 = 异常监控主窗口()
        主窗口.show()
        
        print("✅ 中文GUI界面已显示")
        
        # 运行应用
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保以下文件存在:")
        print("  - 异常监控界面.py")
        print("  - 异常检测器.py")
        print("请确保PyQt5已安装:")
        print("  venv\\Scripts\\pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供详细错误信息
        import traceback
        print("\n详细错误信息:")
        traceback.print_exc()
        
        print("\n🔧 解决建议:")
        print("1. 使用英文版GUI: python 启动英文GUI.py")
        print("2. 使用命令行模式: python 异常检测器.py --health")
        print("3. 检查PyQt5安装: python -c \"import PyQt5; print('OK')\"")
        
        return 1

def 检查环境():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version.split()[0]}")
    
    # 检查编码设置
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    # 检查环境变量
    pythonioencoding = os.environ.get('PYTHONIOENCODING', '未设置')
    print(f"PYTHONIOENCODING: {pythonioencoding}")
    
    # 检查关键文件
    关键文件 = [
        "异常监控界面.py",
        "异常检测器.py", 
        "异常检测配置.json"
    ]
    
    print("\n📁 检查关键文件:")
    缺失文件 = []
    for 文件 in 关键文件:
        if os.path.exists(文件):
            大小 = os.path.getsize(文件) / 1024
            print(f"  ✅ {文件} ({大小:.1f} KB)")
        else:
            print(f"  ❌ {文件} (缺失)")
            缺失文件.append(文件)
    
    if 缺失文件:
        print(f"\n❌ 缺失关键文件: {', '.join(缺失文件)}")
        return False
    
    # 检查依赖库
    print("\n📦 检查依赖库:")
    依赖库 = ["PyQt5", "watchdog", "psutil"]
    
    缺失依赖 = []
    for 库 in 依赖库:
        try:
            __import__(库)
            print(f"  ✅ {库}")
        except ImportError:
            print(f"  ❌ {库} (未安装)")
            缺失依赖.append(库)
    
    if 缺失依赖:
        print(f"\n❌ 缺失依赖库: {', '.join(缺失依赖)}")
        print("安装命令: venv\\Scripts\\pip install " + " ".join(缺失依赖))
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 头条内容社交工具 - 中文GUI直接启动器")
    print("=" * 60)
    
    # 检查环境
    if not 检查环境():
        print("\n❌ 环境检查失败，无法启动GUI")
        input("按回车键退出...")
        return 1
    
    print("\n" + "=" * 60)
    
    # 设置编码环境
    设置编码环境()
    
    # 启动GUI
    exit_code = 启动中文GUI()
    
    if exit_code == 0:
        print("\n🎉 GUI正常退出")
    else:
        print(f"\n❌ GUI异常退出 (退出码: {exit_code})")
    
    return exit_code

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
        sys.exit(0)
        
    except Exception as e:
        print(f"\n💥 启动器异常: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)
