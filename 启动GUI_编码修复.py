#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - GUI启动器 (编码修复版)
=======================================

专门解决中文编码问题的GUI启动器

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import locale

def 设置编码环境():
    """设置正确的编码环境"""
    try:
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'zh_CN.UTF-8'
        
        # 尝试设置系统locale
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, '')
                except:
                    pass
        
        # 设置标准输出编码 (Python 3.7+)
        if hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            except:
                pass
        
        print("✅ 编码环境设置完成")
        return True
        
    except Exception as e:
        print(f"⚠️ 编码环境设置失败: {e}")
        return False

def 启动GUI():
    """启动GUI界面"""
    try:
        print("🖥️ 正在启动GUI界面...")
        
        # 导入GUI模块
        from 异常监控界面 import main as gui_main
        
        # 启动GUI
        gui_main()
        
        return 0
        
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保以下文件存在:")
        print("  - 异常监控界面.py")
        print("  - 异常检测器.py")
        return 1
        
    except UnicodeEncodeError as e:
        print(f"❌ 编码错误: {e}")
        print("\n解决方案:")
        print("1. 在PowerShell中运行:")
        print("   chcp 65001")
        print("   $env:PYTHONIOENCODING='utf-8'")
        print("2. 或在CMD中运行:")
        print("   chcp 65001")
        print("   set PYTHONIOENCODING=utf-8")
        return 1
        
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供详细的错误信息
        import traceback
        print("\n详细错误信息:")
        traceback.print_exc()
        
        return 1

def 检查环境():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version.split()[0]}")
    
    # 检查编码设置
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    # 检查locale设置
    try:
        当前locale = locale.getlocale()
        print(f"当前locale: {当前locale}")
    except:
        print("locale: 无法获取")
    
    # 检查环境变量
    pythonioencoding = os.environ.get('PYTHONIOENCODING', '未设置')
    print(f"PYTHONIOENCODING: {pythonioencoding}")
    
    # 检查关键文件
    关键文件 = [
        "异常监控界面.py",
        "异常检测器.py", 
        "异常检测配置.json"
    ]
    
    print("\n📁 检查关键文件:")
    for 文件 in 关键文件:
        if os.path.exists(文件):
            大小 = os.path.getsize(文件) / 1024
            print(f"  ✅ {文件} ({大小:.1f} KB)")
        else:
            print(f"  ❌ {文件} (缺失)")
    
    # 检查依赖库
    print("\n📦 检查依赖库:")
    依赖库 = ["PyQt5", "watchdog", "psutil"]
    
    for 库 in 依赖库:
        try:
            __import__(库)
            print(f"  ✅ {库}")
        except ImportError:
            print(f"  ❌ {库} (未安装)")

def main():
    """主函数"""
    print("🎯 头条内容社交工具 - GUI启动器 (编码修复版)")
    print("=" * 60)
    
    # 检查环境
    检查环境()
    
    print("\n" + "=" * 60)
    
    # 设置编码环境
    设置编码环境()
    
    # 启动GUI
    return 启动GUI()

if __name__ == "__main__":
    try:
        exit_code = main()
        
        if exit_code == 0:
            print("\n🎉 GUI启动成功！")
        else:
            print(f"\n❌ GUI启动失败 (退出码: {exit_code})")
            
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
        sys.exit(0)
        
    except Exception as e:
        print(f"\n💥 启动器异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
