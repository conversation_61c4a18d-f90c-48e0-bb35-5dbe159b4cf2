# 🔍 头条工具检查功能说明

## 📋 功能概述

我已经专门为您的头条工具创建了**头条工具状态检查**功能，专门检查您的头条内容社交工具是否正在运行，帮助您监控工具的启动状态。

## 🎯 功能特点

### ✅ **专门针对头条工具**
- **🎯 精准检测** - 专门检查头条工具相关进程
- **🔍 智能识别** - 自动识别exe程序和Python脚本
- **📊 详细信息** - 显示进程ID、启动时间、命令行参数
- **🎨 专用界面** - 专门的头条工具状态检查窗口
- **📝 状态记录** - 在系统日志中记录检查结果

### 🎯 **检测范围**
系统会检查以下类型的头条工具进程：

#### 1. 可执行文件
- 头条内容社交工具.exe
- toutiao_tool.exe
- 头条工具.exe
- 今日头条工具.exe

#### 2. Python脚本进程
自动检测包含以下关键词的Python脚本：
- toutiao (头条)
- 头条
- main.py
- app.py
- 启动
- 异常监控

## 🖥️ 使用方法

### 1. 启动检查

1. **打开原生GUI界面**
   ```bash
   venv\Scripts\python.exe 启动原生异常监控.py
   ```

2. **点击检查按钮**
   - 在左侧控制面板找到 **"检查头条工具"** 按钮
   - 点击按钮开始检查

3. **查看检查过程**
   - 状态栏显示 "正在检查头条工具状态..."
   - 系统状态选项卡显示检查进度

### 2. 查看结果

#### 主界面显示
- **系统状态选项卡** - 显示详细的检查结果文本
- **日志输出选项卡** - 记录检查操作日志
- **状态栏** - 显示检查完成状态

#### 专用结果窗口
检查完成后会自动弹出 **"头条工具启动状态检查"** 窗口：

**如果发现头条工具进程：**
- ✅ 显示绿色成功提示
- 📊 表格显示所有相关进程
- 📋 包含进程描述、进程ID、启动时间、命令行参数

**如果未发现头条工具进程：**
- ❌ 显示红色警告提示
- 💡 提供可能原因分析
- 📝 给出操作建议

### 3. 操作选项

- **重新检查** - 重新执行头条工具状态检查
- **关闭** - 关闭详细结果窗口

## 📊 界面展示

### 控制面板按钮
```
┌─────────────────┐
│    控制面板      │
├─────────────────┤
│  系统健康度      │
│   [评分显示]     │
├─────────────────┤
│   异常统计       │
│  [统计数据]      │
├─────────────────┤
│     操作         │
│ [健康度检查]     │
│ [生成报告]       │
│ [开始监控]       │
│ [打开报告目录]   │
│ [刷新数据]       │
│ [检查头条工具] ← 专用按钮
└─────────────────┘
```

### 检查结果窗口

#### 发现进程时：
```
┌─────────────────────────────────────────────────┐
│           头条工具启动状态检查结果                │
├─────────────────────────────────────────────────┤
│        ✅ 发现 2 个头条工具相关进程              │
├─────────────────────────────────────────────────┤
│进程描述              │进程ID│启动时间  │命令行    │
│头条工具Python脚本    │1234 │10:30:15 │python... │
│头条内容社交工具.exe  │5678 │11:45:22 │toutiao...│
├─────────────────────────────────────────────────┤
│                [重新检查] [关闭]                 │
└─────────────────────────────────────────────────┘
```

#### 未发现进程时：
```
┌─────────────────────────────────────────────────┐
│           头条工具启动状态检查结果                │
├─────────────────────────────────────────────────┤
│        ❌ 未发现头条工具相关进程                 │
├─────────────────────────────────────────────────┤
│ 可能的原因和建议                                │
│ ┌─────────────────────────────────────────────┐ │
│ │ 可能的原因：                                │ │
│ │ • 头条工具未启动                            │ │
│ │ • 进程名称不在检测范围内                    │ │
│ │ • 权限不足无法访问进程信息                  │ │
│ │                                           │ │
│ │ 建议操作：                                  │ │
│ │ • 检查头条工具是否正常启动                  │ │
│ │ • 确认工具的实际进程名称                    │ │
│ │ • 以管理员身份运行监控程序                  │ │
│ │ • 手动启动头条工具后重新检查                │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│                [重新检查] [关闭]                 │
└─────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 检测逻辑
1. **进程枚举** - 使用psutil获取所有运行中的进程
2. **名称匹配** - 检查进程名是否包含头条相关关键词
3. **命令行分析** - 分析Python进程的脚本路径和参数
4. **智能识别** - 区分头条工具和其他无关进程

### 检测精度
- **准确性** - 专门针对头条工具，减少误报
- **全面性** - 同时检测exe程序和Python脚本
- **详细性** - 提供完整的进程信息

## 📈 使用场景

### 1. 启动验证
- 启动头条工具后验证是否成功运行
- 确认工具的所有组件都正常启动
- 检查是否有多个实例在运行

### 2. 故障排查
- 工具异常时检查进程状态
- 确认工具是否意外退出
- 分析进程启动时间和参数

### 3. 性能监控
- 监控工具的运行状态
- 检查进程ID变化情况
- 分析工具重启频率

## 💡 使用建议

### 1. 定期检查
- 每次使用异常监控系统时检查头条工具状态
- 发现异常时首先检查工具是否正常运行

### 2. 结合异常监控
- 配合健康度检查使用
- 结合异常日志分析
- 关联进程状态和异常情况

### 3. 故障诊断
- 工具无响应时检查进程状态
- 异常频发时确认工具运行情况
- 性能问题时分析进程信息

## 🔍 故障排除

### 常见问题

#### 1. 检查失败
**现象**: 点击按钮后显示检查失败
**解决**: 
```bash
# 确认psutil库已安装
venv\Scripts\pip install psutil
```

#### 2. 检测不到头条工具
**现象**: 工具在运行但显示未发现
**可能原因**:
- 进程名称不在检测范围内
- 权限不足
- 工具以特殊方式启动

**解决方案**:
- 以管理员身份运行监控程序
- 检查工具的实际进程名称
- 联系开发者添加新的检测关键词

#### 3. 权限错误
**现象**: 显示访问被拒绝
**解决**: 以管理员身份运行程序

## 🎉 功能优势

### 相比通用进程检查
- ✅ **专门针对** - 只关注头条工具相关进程
- ✅ **智能识别** - 自动区分相关和无关进程
- ✅ **详细信息** - 提供工具特定的进程信息
- ✅ **用户友好** - 专门的界面和提示

### 相比手动查看
- ✅ **自动化** - 一键检查所有相关进程
- ✅ **准确性** - 避免手动查找的遗漏
- ✅ **便捷性** - 集成在监控系统中
- ✅ **记录性** - 自动记录检查历史

---

**🎯 头条工具检查功能让您随时掌握工具的运行状态！**

*功能版本: 1.1.0*  
*更新时间: 2025-08-01*  
*专门针对: 头条内容社交工具*
