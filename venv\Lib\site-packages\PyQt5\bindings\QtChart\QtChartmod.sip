// QtChartmod.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtChart, keyword_arguments="Optional", use_limited_api=True)

%Import QtGui/QtGuimod.sip
%Import QtWidgets/QtWidgetsmod.sip

%Timeline {QtChart_1_0_0 QtChart_1_1_0 QtChart_1_2_0 QtChart_1_2_1 QtChart_1_3_0 QtChart_1_3_1 QtChart_1_4_0 QtChart_2_0_0 QtChart_2_0_1 QtChart_2_1_0 QtChart_5_7_0 QtChart_5_7_1 QtChart_5_8_0 QtChart_5_9_0 QtChart_5_9_1 QtChart_5_9_2 QtChart_5_9_3 QtChart_5_9_4 QtChart_5_9_5 QtChart_5_9_6 QtChart_5_9_7 QtChart_5_9_8 QtChart_5_9_9 QtChart_5_10_0 QtChart_5_10_1 QtChart_5_11_0 QtChart_5_11_1 QtChart_5_11_2 QtChart_5_11_3 QtChart_5_12_0 QtChart_5_12_1 QtChart_5_12_2 QtChart_5_12_3 QtChart_5_12_4 QtChart_5_13_0 QtChart_5_14_0 QtChart_5_15_0}

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQtChart.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper
%HideNamespace QtCharts

%Include qabstractaxis.sip
%Include qabstractbarseries.sip
%Include qabstractseries.sip
%Include qarealegendmarker.sip
%Include qareaseries.sip
%Include qbarcategoryaxis.sip
%Include qbarcategoriesaxis.sip
%Include qbarlegendmarker.sip
%Include qbarseries.sip
%Include qbarset.sip
%Include qboxplotlegendmarker.sip
%Include qboxplotseries.sip
%Include qboxset.sip
%Include qcandlesticklegendmarker.sip
%Include qcandlestickmodelmapper.sip
%Include qcandlestickseries.sip
%Include qcandlestickset.sip
%Include qcategoryaxis.sip
%Include qchart.sip
%Include qchartglobal.sip
%Include qchartview.sip
%Include qdatetimeaxis.sip
%Include qhbarmodelmapper.sip
%Include qhboxplotmodelmapper.sip
%Include qhcandlestickmodelmapper.sip
%Include qhorizontalbarseries.sip
%Include qhorizontalpercentbarseries.sip
%Include qhorizontalstackedbarseries.sip
%Include qhpiemodelmapper.sip
%Include qhxymodelmapper.sip
%Include qlegend.sip
%Include qlegendmarker.sip
%Include qlineseries.sip
%Include qlogvalueaxis.sip
%Include qpercentbarseries.sip
%Include qpielegendmarker.sip
%Include qpieseries.sip
%Include qpieslice.sip
%Include qpolarchart.sip
%Include qscatterseries.sip
%Include qsplineseries.sip
%Include qstackedbarseries.sip
%Include qvalueaxis.sip
%Include qvaluesaxis.sip
%Include qvbarmodelmapper.sip
%Include qvboxplotmodelmapper.sip
%Include qvcandlestickmodelmapper.sip
%Include qvpiemodelmapper.sip
%Include qvxymodelmapper.sip
%Include qxylegendmarker.sip
%Include qxyseries.sip
