// qchartview.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qchartview.h>
%End

    class QChartView : public QGraphicsView
    {
%TypeHeaderCode
#include <qchartview.h>
%End

    public:
        enum RubberBand
        {
            NoRubberBand,
            VerticalRubberBand,
%If (QtChart_2_0_0 -)
            HorizontalRubberBand,
%End
%If (- QtChart_2_0_0)
            HorizonalRubberBand,
%End
            RectangleRubberBand,
        };

        typedef QFlags<QtCharts::QChartView::RubberBand> RubberBands;
        explicit QChartView(QWidget *parent /TransferThis/ = 0);
        QChartView(QtCharts::QChart *chart /Transfer/, QWidget *parent /TransferThis/ = 0);
        virtual ~QChartView();
        void setRubberBand(const QtCharts::QChartView::RubberBands &rubberBands);
        QtCharts::QChartView::RubberBands rubberBand() const;
%If (QtChart_1_2_0 -)
        void setChart(QtCharts::QChart *chart /Transfer/);
%MethodCode
            // The ownership of any existing chart must be passed back to Python.
            #if defined(QT_CHARTS_VERSION)
            QtCharts::QChart *c = sipCpp->chart();
            #else
            QtCommercialChart::QChart *c = sipCpp->chart();
            #endif
            
            Py_BEGIN_ALLOW_THREADS
            sipCpp->setChart(a0);
            Py_END_ALLOW_THREADS
            
            if (c)
            {
                PyObject *co = sipGetPyObject(c, sipType_QtCharts_QChart);
            
                if (co)
                    sipTransferBack(co);
            }
%End

%End
        QtCharts::QChart *chart() const;

    protected:
        virtual void resizeEvent(QResizeEvent *event);
        virtual void mousePressEvent(QMouseEvent *event);
        virtual void mouseMoveEvent(QMouseEvent *event);
        virtual void mouseReleaseEvent(QMouseEvent *event);
%If (QtChart_5_14_0 -)
        virtual void wheelEvent(QWheelEvent *event);
%End
    };
};
