// qcategoryaxis.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_1_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qcategoryaxis.h>
%End

    class QCategoryAxis : public QtCharts::QValueAxis
    {
%TypeHeaderCode
#include <qcategoryaxis.h>
%End

    public:
        explicit QCategoryAxis(QObject *parent /TransferThis/ = 0);
        virtual ~QCategoryAxis();
        virtual QtCharts::QAbstractAxis::AxisType type() const;
        void append(const QString &label, qreal categoryEndValue);
        void remove(const QString &label);
        void replaceLabel(const QString &oldLabel, const QString &newLabel);
        qreal startValue(const QString &categoryLabel = QString()) const;
        void setStartValue(qreal min);
        qreal endValue(const QString &categoryLabel) const;
        QStringList categoriesLabels();
        int count() const /__len__/;

    signals:
%If (QtChart_1_3_0 -)
        void categoriesChanged();
%End

    public:
%If (QtChart_2_1_0 -)

        enum AxisLabelsPosition
        {
            AxisLabelsPositionCenter,
            AxisLabelsPositionOnValue,
        };

%End
%If (QtChart_2_1_0 -)
        QtCharts::QCategoryAxis::AxisLabelsPosition labelsPosition() const;
%End
%If (QtChart_2_1_0 -)
        void setLabelsPosition(QtCharts::QCategoryAxis::AxisLabelsPosition position);
%End

    signals:
%If (QtChart_2_1_0 -)
        void labelsPositionChanged(QtCharts::QCategoryAxis::AxisLabelsPosition position /ScopesStripped=1/);
%End
    };
};

%End
