# 🔍 实时监控功能详细说明

## 📋 功能概述

实时监控功能是全面异常检测系统的核心组件，专门用于**自动捕捉运行日志中的问题**，包括闪退、报错原因等关键异常。系统能够智能识别和分析各种类型的异常，并提供实时告警。

## 🎯 核心特性

### ✅ **智能异常识别**
系统使用正则表达式模式匹配，能够识别以下7大类异常：

#### 1. 程序闪退 🚨
- **检测模式**: crash、crashed、崩溃、闪退、异常退出
- **严重程度**: Critical
- **触发通知**: 是
- **示例**: "FATAL ERROR: Application crashed unexpectedly"

#### 2. 登录失败 🔐
- **检测模式**: login fail、登录失败、cookie expire、账号禁言
- **严重程度**: Error
- **触发通知**: 否
- **示例**: "ERROR: Login failed - invalid credentials"

#### 3. 网络错误 🌐
- **检测模式**: connection timeout、网络错误、DNS error
- **严重程度**: Warning
- **触发通知**: 否
- **示例**: "ERROR: Connection timeout after 30 seconds"

#### 4. 文件操作错误 📁
- **检测模式**: file not found、权限不足、磁盘已满
- **严重程度**: Error
- **触发通知**: 否
- **示例**: "ERROR: File not found: /path/to/file.txt"

#### 5. 内存错误 💾
- **检测模式**: out of memory、内存泄漏、heap overflow
- **严重程度**: Critical
- **触发通知**: 是
- **示例**: "CRITICAL: Out of memory - cannot allocate buffer"

#### 6. 线程错误 🧵
- **检测模式**: thread error、deadlock、QThread error
- **严重程度**: Critical
- **触发通知**: 是
- **示例**: "ERROR: Thread synchronization failed"

#### 7. 数据处理错误 📊
- **检测模式**: parse error、编码错误、data corrupt
- **严重程度**: Warning
- **触发通知**: 否
- **示例**: "ERROR: JSON parse error - invalid format"

## 🖥️ 界面功能

### 📡 **实时监控选项卡**

#### 监控控制面板
- **监控状态**: 显示当前监控状态（运行中/已停止）
- **监控文件数**: 实时显示正在监控的日志文件数量
- **检测异常数**: 实时显示检测到的异常总数

#### 实时异常流
- **滚动显示**: 自动滚动显示最新检测到的异常
- **详细信息**: 包含时间、文件、类型、行号、具体内容
- **自动清理**: 超过500行时自动清理旧记录，保留最新400行
- **格式化显示**: 每条异常用分隔线分开，便于阅读

### 🔔 **告警通知系统**

#### 严重问题通知
当检测到以下严重问题时，系统会弹出桌面通知：
- 程序闪退
- 内存错误  
- 线程错误

#### 通知内容
- **单个问题**: 显示具体的问题类型、文件和详情
- **多个问题**: 显示问题数量和类型汇总
- **状态记录**: 在系统状态中记录通知信息

## 🔧 技术实现

### ⚡ **实时监控机制**

#### 文件监控
- **监控频率**: 每10秒检查一次logs目录
- **文件类型**: app_*.log、crash_log_*.txt、toutiao_*.log、*.log
- **变化检测**: 检测最近5分钟内修改的文件
- **增量读取**: 只读取文件的新增内容，避免重复处理

#### 智能分析
- **位置记录**: 记录每个文件的已处理位置
- **模式匹配**: 使用正则表达式进行智能模式匹配
- **行级分析**: 逐行分析日志内容，精确定位问题
- **上下文提取**: 提取问题的上下文信息

### 🛡️ **性能优化**

#### 资源管理
- **低CPU占用**: 监控线程CPU使用率 < 5%
- **内存控制**: 自动清理旧记录，控制内存使用
- **异步处理**: 使用后台线程，不阻塞界面
- **错误容错**: 单个文件分析失败不影响整体监控

#### 效率提升
- **增量处理**: 只处理文件的新增部分
- **智能过滤**: 跳过空行和无关内容
- **批量更新**: 批量更新界面，减少频繁刷新

## 📊 监控数据

### 📈 **实时统计**
- **监控文件数**: 当前正在监控的日志文件数量
- **检测异常数**: 累计检测到的异常数量
- **模块状态**: 各功能模块的异常统计和状态

### 📋 **异常记录格式**
```
[时间] 🚨 异常类型 - 文件名:行号
    具体异常内容...
============================================================
```

### 🔍 **详细信息**
每条异常记录包含：
- **时间戳**: 精确到秒的检测时间
- **文件信息**: 异常所在的日志文件名
- **异常类型**: 7大类异常类型之一
- **行号**: 异常在文件中的具体行号
- **内容**: 异常的具体描述（截取前100字符）

## 🚀 使用指南

### 📋 **启动监控**
1. 打开原生异常监控界面
2. 切换到"实时监控"选项卡
3. 点击左侧控制面板的"开始监控"按钮
4. 观察监控状态变为"运行中"（绿色）

### 👀 **查看异常**
1. 在实时异常流区域查看检测到的异常
2. 异常按时间顺序显示，最新的在底部
3. 每条异常都有详细的时间、文件、类型信息
4. 严重异常会触发桌面通知弹窗

### ⏹️ **停止监控**
1. 点击"停止监控"按钮
2. 监控状态变为"已停止"（红色）
3. 停止检测新的异常，但保留已检测的记录

## 🧪 测试功能

### 📝 **测试脚本**
使用 `测试实时监控功能.py` 脚本可以：
- 生成各种类型的测试异常日志
- 模拟实时异常生成
- 生成严重问题测试日志
- 验证监控功能的准确性

### 🎯 **测试步骤**
```bash
# 1. 生成测试日志
python 测试实时监控功能.py

# 2. 选择测试模式（建议选择4-全部测试）
# 3. 启动监控界面
python 启动原生异常监控.py

# 4. 在实时监控选项卡中开始监控
# 5. 观察异常捕捉情况
```

## 💡 实际应用场景

### 🔍 **日常监控**
- **开发调试**: 实时捕捉程序运行中的异常
- **生产环境**: 监控线上系统的稳定性
- **问题排查**: 快速定位和分析问题原因

### 🚨 **异常处理**
- **闪退监控**: 及时发现程序崩溃问题
- **性能监控**: 监控内存和线程问题
- **网络监控**: 跟踪网络连接异常

### 📊 **数据分析**
- **趋势分析**: 观察异常发生的时间模式
- **问题分类**: 统计不同类型异常的频率
- **模块评估**: 评估各功能模块的稳定性

## ⚠️ 注意事项

### 🔧 **配置要求**
- 确保logs目录存在且可读
- 日志文件需要使用UTF-8编码
- 系统需要有足够的文件访问权限

### 📈 **性能考虑**
- 大量日志文件可能影响监控性能
- 建议定期清理旧日志文件
- 监控间隔可根据需要调整

### 🛡️ **稳定性保障**
- 单个文件分析失败不影响整体监控
- 系统会自动处理编码错误
- 监控线程异常不会导致界面崩溃

---

**🎯 实时监控功能让您能够及时发现和处理系统中的各种异常问题！**

*功能版本: 2.1.0*  
*更新时间: 2025-08-01*  
*特点: 智能识别、实时告警、自动捕捉*
