#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的GUI测试程序
================

测试PyQt5是否能正常工作，避免复杂的中文编码问题。

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os

def test_gui():
    """测试GUI功能"""
    try:
        # 设置编码环境
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 导入PyQt5
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
        from PyQt5.QtCore import Qt
        
        print("✅ PyQt5导入成功")
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("Exception Monitor Test")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加标签
        label = QLabel("GUI Test Successful!")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton("Close")
        button.clicked.connect(window.close)
        layout.addWidget(button)
        
        # 显示窗口
        window.show()
        
        print("✅ GUI窗口已显示")
        
        # 运行应用
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return 1

if __name__ == "__main__":
    print("🧪 开始GUI测试...")
    exit_code = test_gui()
    print(f"测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
