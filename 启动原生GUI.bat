@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8

echo 🎯 头条内容社交工具 - 原生异常监控系统
echo =======================================
echo 使用Python内置tkinter，无需安装PyQt5
echo.

REM 检查虚拟环境
if not exist "venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在
    echo 请先创建虚拟环境: python -m venv venv
    pause
    exit /b 1
)

echo ✅ 虚拟环境检查通过

REM 检查核心文件
if not exist "原生异常监控界面.py" (
    echo ❌ 原生异常监控界面.py 文件不存在
    pause
    exit /b 1
)

if not exist "异常检测器.py" (
    echo ❌ 异常检测器.py 文件不存在
    pause
    exit /b 1
)

echo ✅ 核心文件检查通过
echo.

echo 🖥️ 启动原生GUI界面...
echo 💡 特点: 无依赖、启动快、内存占用低
echo.

venv\Scripts\python.exe 启动原生异常监控.py

echo.
echo 👋 原生GUI已关闭
pause
