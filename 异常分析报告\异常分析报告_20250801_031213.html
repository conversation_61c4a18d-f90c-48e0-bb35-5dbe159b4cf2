
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头条内容社交工具 - 异常分析报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .health-score { text-align: center; margin: 20px 0; }
        .score-circle { display: inline-block; width: 120px; height: 120px; border-radius: 50%;
                        background: conic-gradient(#4CAF50 0deg 360.0deg, #e0e0e0 360.0deg 360deg);
                        position: relative; }
        .score-text { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                      font-size: 24px; font-weight: bold; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .stat-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .stat-value { font-size: 18px; color: #007bff; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .severity-critical { color: #dc3545; font-weight: bold; }
        .severity-error { color: #fd7e14; font-weight: bold; }
        .severity-warning { color: #ffc107; font-weight: bold; }
        .severity-info { color: #17a2b8; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .recommendations h3 { color: #0066cc; margin-top: 0; }
        .recommendations ul { margin: 10px 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 头条内容社交工具 - 异常分析报告</h1>
            <p>生成时间: 2025-08-01T03:12:13.580535</p>
        </div>

        <div class="health-score">
            <h2>系统健康度评分</h2>
            <div class="score-circle">
                <div class="score-text">100.0</div>
            </div>
            <p>评级: <strong>优秀</strong></p>
        </div>

        <div class="stats-grid">

            <div class="stat-card">
                <div class="stat-title">1小时内异常统计</div>
                <div class="stat-value">总计: 0 个</div>
                <div style="margin-top: 10px;">

                </div>
            </div>

            <div class="stat-card">
                <div class="stat-title">24小时内异常统计</div>
                <div class="stat-value">总计: 0 个</div>
                <div style="margin-top: 10px;">

                </div>
            </div>

        </div>

        <h3>📊 详细统计数据</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>时间范围</th>
                    <th>总异常数</th>
                    <th>Critical</th>
                    <th>Error</th>
                    <th>Warning</th>
                    <th>Info</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>1小时</td>
                    <td>0</td>
                    <td class="severity-critical">0</td>
                    <td class="severity-error">0</td>
                    <td class="severity-warning">0</td>
                    <td class="severity-info">0</td>
                </tr>

                <tr>
                    <td>24小时</td>
                    <td>0</td>
                    <td class="severity-critical">0</td>
                    <td class="severity-error">0</td>
                    <td class="severity-warning">0</td>
                    <td class="severity-info">0</td>
                </tr>

                <tr>
                    <td>7天</td>
                    <td>0</td>
                    <td class="severity-critical">0</td>
                    <td class="severity-error">0</td>
                    <td class="severity-warning">0</td>
                    <td class="severity-info">0</td>
                </tr>

                <tr>
                    <td>30天</td>
                    <td>0</td>
                    <td class="severity-critical">0</td>
                    <td class="severity-error">0</td>
                    <td class="severity-warning">0</td>
                    <td class="severity-info">0</td>
                </tr>

            </tbody>
        </table>

        <h3>🖥️ 系统信息</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">操作系统</div>
                <div class="stat-value">Windows-10-10.0.26100-SP0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Python版本</div>
                <div class="stat-value">3.9.13</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">CPU使用率</div>
                <div class="stat-value">22.1%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">内存使用</div>
                <div class="stat-value">75.4% (12237MB / 16236MB)</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 12px;">
            <p>报告由头条内容社交工具异常检测系统自动生成</p>
        </div>
    </div>
</body>
</html>
