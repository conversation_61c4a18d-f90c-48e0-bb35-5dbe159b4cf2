@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8

:menu
cls
echo.
echo 🎯 头条内容社交工具 - 异常监控系统
echo ================================================
echo 当前时间: %date% %time%
echo 虚拟环境: venv\Scripts\python.exe
echo.
echo 📋 功能菜单:
echo [1] 🏥 系统健康度检查
echo [2] 📝 生成分析报告 (HTML+JSON)
echo [3] 📊 查看统计信息
echo [4] 🔍 实时监控 (按Ctrl+C停止)
echo [5] 📈 历史分析 (7天)
echo [6] 🧪 运行系统测试
echo [7] 📂 打开报告目录
echo [8] 🖥️ 启动中文GUI界面 (PyQt5)
echo [9] 🌐 启动英文GUI界面 (PyQt5)
echo [A] 🎯 启动原生GUI界面 (推荐)
echo [0] 🚪 退出
echo.
echo ================================================

set /p choice=请输入选择 (0-9/A):

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto health
if "%choice%"=="2" goto report
if "%choice%"=="3" goto stats
if "%choice%"=="4" goto monitor
if "%choice%"=="5" goto analyze
if "%choice%"=="6" goto test
if "%choice%"=="7" goto opendir
if "%choice%"=="8" goto gui
if "%choice%"=="9" goto english_gui
if /i "%choice%"=="A" goto native_gui

echo 无效选择，请重新输入
timeout /t 2 >nul
goto menu

:health
echo.
echo 🏥 执行系统健康度检查...
echo ================================================
venv\Scripts\python.exe 异常检测器.py --health
echo.
echo ================================================
pause
goto menu

:report
echo.
echo 📝 生成分析报告...
echo ================================================
venv\Scripts\python.exe 异常检测器.py --report --format HTML JSON CSV
echo.
echo ================================================
echo 💡 提示: 报告已生成到 异常分析报告\ 目录
pause
goto menu

:stats
echo.
echo 📊 查看统计信息...
echo ================================================
echo 请选择时间范围:
echo [1] 1小时
echo [2] 24小时  
echo [3] 7天
echo [4] 30天
set /p timerange=请选择 (1-4): 

if "%timerange%"=="1" set timestr=1小时
if "%timerange%"=="2" set timestr=24小时
if "%timerange%"=="3" set timestr=7天
if "%timerange%"=="4" set timestr=30天

if defined timestr (
    echo.
    echo 📈 %timestr%统计信息:
    echo ================================================
    venv\Scripts\python.exe 异常检测器.py --stats %timestr%
) else (
    echo 无效选择
)
echo.
echo ================================================
pause
goto menu

:monitor
echo.
echo 🔍 启动实时监控...
echo ================================================
echo 💡 提示: 按 Ctrl+C 可以停止监控
echo.
venv\Scripts\python.exe 异常检测器.py --monitor
echo.
echo ================================================
pause
goto menu

:analyze
echo.
echo 📈 执行历史分析 (最近7天)...
echo ================================================
venv\Scripts\python.exe 异常检测器.py --analyze 7
echo.
echo ================================================
pause
goto menu

:test
echo.
echo 🧪 运行系统测试...
echo ================================================
if exist "直接测试.py" (
    venv\Scripts\python.exe 直接测试.py
) else (
    echo 测试文件不存在
)
echo.
echo ================================================
pause
goto menu

:opendir
echo.
echo 📂 打开报告目录...
if exist "异常分析报告" (
    explorer "异常分析报告"
    echo ✅ 已打开报告目录
) else (
    echo ❌ 报告目录不存在，请先生成报告
)
timeout /t 2 >nul
goto menu

:gui
echo.
echo 🖥️ 启动中文GUI界面...
echo ================================================
echo ⚠️ 注意: 中文GUI可能存在编码问题
echo 如果启动失败，请选择英文GUI或命令行功能
echo.

if exist "简单启动器.py" (
    echo 使用简单启动器...
    venv\Scripts\python.exe 简单启动器.py
) else if exist "启动GUI_编码修复.py" (
    echo 使用编码修复启动器...
    venv\Scripts\python.exe 启动GUI_编码修复.py
) else (
    echo 使用标准启动器...
    venv\Scripts\python.exe 启动异常监控.py
)

echo.
echo ================================================
pause
goto menu

:english_gui
echo.
echo 🌐 启动英文GUI界面...
echo ================================================
echo ✅ 英文GUI避免了编码问题，推荐使用
echo.

if exist "启动英文GUI.py" (
    echo 启动英文版异常监控界面...
    venv\Scripts\python.exe 启动英文GUI.py
) else if exist "测试GUI.py" (
    echo 启动测试GUI...
    venv\Scripts\python.exe 测试GUI.py
) else (
    echo 英文GUI文件不存在
)

echo.
echo ================================================
pause
goto menu

:native_gui
echo.
echo 🎯 启动原生GUI界面...
echo ================================================
echo ✅ 使用Python内置tkinter，无需PyQt5依赖
echo ✅ 启动速度快，内存占用低
echo ✅ 完整的异常监控功能
echo.

if exist "启动原生异常监控.py" (
    echo 启动原生异常监控界面...
    venv\Scripts\python.exe 启动原生异常监控.py
) else (
    echo 原生GUI文件不存在
)

echo.
echo ================================================
pause
goto menu

:exit
echo.
echo 👋 感谢使用异常监控系统！
echo.
timeout /t 2 >nul
exit /b 0
