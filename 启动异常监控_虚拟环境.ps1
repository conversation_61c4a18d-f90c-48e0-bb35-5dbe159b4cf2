# 头条内容社交工具 - 异常监控系统启动脚本 (PowerShell版本)
# 适用于虚拟环境

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔍 头条内容社交工具 - 异常监控系统" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# 检查虚拟环境
$venvPython = "venv\Scripts\python.exe"
if (-not (Test-Path $venvPython)) {
    Write-Host "❌ 虚拟环境不存在，请先创建虚拟环境" -ForegroundColor Red
    Write-Host "创建命令: python -m venv venv" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 检测到虚拟环境" -ForegroundColor Green
Write-Host ""

# 主菜单循环
do {
    Write-Host "请选择启动模式:" -ForegroundColor White
    Write-Host "[1] GUI界面模式" -ForegroundColor Yellow
    Write-Host "[2] 命令行 - 健康度检查" -ForegroundColor Yellow
    Write-Host "[3] 命令行 - 生成报告" -ForegroundColor Yellow
    Write-Host "[4] 命令行 - 实时监控" -ForegroundColor Yellow
    Write-Host "[5] 命令行 - 帮助信息" -ForegroundColor Yellow
    Write-Host "[0] 退出" -ForegroundColor Red
    Write-Host ""
    
    $choice = Read-Host "请输入选择 (0-5)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "🖥️ 启动GUI界面..." -ForegroundColor Green
            & $venvPython "启动异常监控.py"
        }
        "2" {
            Write-Host ""
            Write-Host "🏥 执行健康度检查..." -ForegroundColor Green
            & $venvPython "异常检测器.py" "--health"
            Write-Host ""
            Read-Host "按回车键继续"
        }
        "3" {
            Write-Host ""
            Write-Host "📝 生成分析报告..." -ForegroundColor Green
            & $venvPython "异常检测器.py" "--report" "--format" "HTML" "JSON" "CSV"
            Write-Host ""
            Read-Host "按回车键继续"
        }
        "4" {
            Write-Host ""
            Write-Host "🔍 启动实时监控 (按Ctrl+C停止)..." -ForegroundColor Green
            & $venvPython "异常检测器.py" "--monitor"
            Write-Host ""
            Read-Host "按回车键继续"
        }
        "5" {
            Write-Host ""
            Write-Host "📚 异常检测器帮助信息:" -ForegroundColor Green
            & $venvPython "异常检测器.py" "--help"
            Write-Host ""
            Read-Host "按回车键继续"
        }
        "0" {
            Write-Host ""
            Write-Host "👋 感谢使用异常监控系统！" -ForegroundColor Cyan
            break
        }
        default {
            Write-Host "无效选择，请重新输入" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    
} while ($choice -ne "0")

Read-Host "按回车键退出"
