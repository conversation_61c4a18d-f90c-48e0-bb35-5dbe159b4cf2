# 头条内容社交工具 - GUI启动脚本 (PowerShell版本)
# 解决中文编码问题

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# 设置环境变量
$env:PYTHONIOENCODING = "utf-8"
$env:LANG = "zh_CN.UTF-8"

Write-Host "🎯 头条内容社交工具 - GUI启动脚本" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# 检查虚拟环境
$venvPython = "venv\Scripts\python.exe"
if (-not (Test-Path $venvPython)) {
    Write-Host "❌ 虚拟环境不存在" -ForegroundColor Red
    Write-Host "请先创建虚拟环境: python -m venv venv" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 找到虚拟环境: $venvPython" -ForegroundColor Green

# 检查关键文件
$keyFiles = @("异常监控界面.py", "异常检测器.py", "异常检测配置.json")
$missingFiles = @()

Write-Host "`n📁 检查关键文件:" -ForegroundColor White
foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length / 1KB
        Write-Host "  ✅ $file ($([math]::Round($size, 1)) KB)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`n❌ 缺失关键文件，无法启动" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 启动GUI
Write-Host "`n🖥️ 启动GUI界面..." -ForegroundColor Green
Write-Host "提示: 如果出现编码错误，请确保PowerShell支持UTF-8" -ForegroundColor Yellow

try {
    # 使用简单启动器
    if (Test-Path "简单启动器.py") {
        Write-Host "使用简单启动器..." -ForegroundColor Cyan
        & $venvPython "简单启动器.py"
    } elseif (Test-Path "启动GUI_编码修复.py") {
        Write-Host "使用编码修复启动器..." -ForegroundColor Cyan
        & $venvPython "启动GUI_编码修复.py"
    } else {
        Write-Host "使用标准启动器..." -ForegroundColor Cyan
        & $venvPython "启动异常监控.py"
    }
    
    Write-Host "`n🎉 GUI启动完成" -ForegroundColor Green
    
} catch {
    Write-Host "`n❌ GUI启动失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n🔧 故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 确保所有依赖已安装: venv\Scripts\pip install PyQt5 watchdog psutil" -ForegroundColor White
    Write-Host "2. 尝试在CMD中运行: chcp 65001 && set PYTHONIOENCODING=utf-8" -ForegroundColor White
    Write-Host "3. 使用命令行模式: venv\Scripts\python.exe 异常检测器.py --health" -ForegroundColor White
}

Read-Host "`n按回车键退出"
