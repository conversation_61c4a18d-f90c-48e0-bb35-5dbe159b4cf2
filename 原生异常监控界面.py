#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - 原生异常监控界面
================================

使用tkinter原生GUI库，无需安装PyQt5等第三方依赖。

作者: AI Assistant
版本: 1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
import os
from datetime import datetime
import sys

class 原生异常监控主窗口:
    """原生异常监控主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.异常检测器 = None
        self.监控线程 = None
        self.监控运行中 = False
        
        self.初始化界面()
        self.初始化异常检测器()
        self.更新状态()
    
    def 初始化界面(self):
        """初始化用户界面"""
        # 设置窗口
        self.root.title("头条内容社交工具 - 异常监控系统")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="异常监控系统", 
                               font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        self.创建控制面板(main_frame)
        
        # 右侧主要内容区域
        self.创建内容区域(main_frame)
        
        # 底部状态栏
        self.创建状态栏(main_frame)
    
    def 创建控制面板(self, parent):
        """创建左侧控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                          padx=(0, 10))
        
        # 系统健康度显示
        health_frame = ttk.LabelFrame(control_frame, text="系统健康度", padding="10")
        health_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.健康度分数 = ttk.Label(health_frame, text="--", 
                                font=("微软雅黑", 20, "bold"), foreground="red")
        self.健康度分数.pack()
        
        self.健康度等级 = ttk.Label(health_frame, text="未检测", 
                                font=("微软雅黑", 12))
        self.健康度等级.pack()
        
        # 异常统计
        stats_frame = ttk.LabelFrame(control_frame, text="异常统计", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.critical_count = ttk.Label(stats_frame, text="严重: 0", foreground="red")
        self.critical_count.pack(anchor=tk.W)
        
        self.error_count = ttk.Label(stats_frame, text="错误: 0", foreground="orange")
        self.error_count.pack(anchor=tk.W)
        
        self.warning_count = ttk.Label(stats_frame, text="警告: 0", foreground="blue")
        self.warning_count.pack(anchor=tk.W)
        
        self.info_count = ttk.Label(stats_frame, text="信息: 0", foreground="green")
        self.info_count.pack(anchor=tk.W)
        
        # 操作按钮
        button_frame = ttk.LabelFrame(control_frame, text="操作", padding="10")
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="健康度检查", 
                  command=self.执行健康度检查).pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="生成报告", 
                  command=self.生成报告).pack(fill=tk.X, pady=2)
        
        self.监控按钮 = ttk.Button(button_frame, text="开始监控", 
                                command=self.切换监控状态)
        self.监控按钮.pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="打开报告目录", 
                  command=self.打开报告目录).pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="刷新数据",
                  command=self.刷新数据).pack(fill=tk.X, pady=2)

        ttk.Button(button_frame, text="检查存稿工具",
                  command=self.检查软件状态).pack(fill=tk.X, pady=2)

        ttk.Button(button_frame, text="导出报告",
                  command=self.导出详细报告).pack(fill=tk.X, pady=2)

        ttk.Button(button_frame, text="清理日志",
                  command=self.清理旧日志).pack(fill=tk.X, pady=2)
    
    def 创建内容区域(self, parent):
        """创建右侧内容区域"""
        content_frame = ttk.Frame(parent)
        content_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(1, weight=1)
        
        # 选项卡
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 异常日志选项卡
        self.创建异常日志选项卡()

        # 功能模块监控选项卡
        self.创建功能模块监控选项卡()

        # 实时监控选项卡
        self.创建实时监控选项卡()

        # 异常统计图表选项卡
        self.创建异常统计图表选项卡()

        # 系统状态选项卡
        self.创建系统状态选项卡()

        # 日志输出选项卡
        self.创建日志输出选项卡()
    
    def 创建异常日志选项卡(self):
        """创建异常日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="异常日志")
        
        # 创建表格
        columns = ("时间", "文件", "异常类型", "严重程度", "描述")
        self.异常表格 = ttk.Treeview(log_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.异常表格.heading(col, text=col)
            self.异常表格.column(col, width=120)
        
        # 滚动条
        scrollbar_y = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.异常表格.yview)
        scrollbar_x = ttk.Scrollbar(log_frame, orient=tk.HORIZONTAL, command=self.异常表格.xview)
        self.异常表格.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.异常表格.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def 创建功能模块监控选项卡(self):
        """创建功能模块监控选项卡"""
        module_frame = ttk.Frame(self.notebook)
        self.notebook.add(module_frame, text="功能模块")

        # 创建模块监控面板
        modules_info = [
            ("账号管理模块", "监控登录失败、Cookie过期、账号状态异常"),
            ("数据采集模块", "监控网络超时、数据解析错误、API调用失败"),
            ("视频处理模块", "监控文件损坏、编码错误、处理超时"),
            ("AI改写模块", "监控服务连接失败、内容生成错误"),
            ("自动化操作", "监控浏览器驱动问题、页面加载失败"),
            ("安全防护模块", "监控反破解检测、权限验证失败")
        ]

        # 创建模块状态显示
        self.模块状态字典 = {}
        for i, (模块名, 描述) in enumerate(modules_info):
            # 模块框架
            module_group = ttk.LabelFrame(module_frame, text=模块名, padding="5")
            module_group.grid(row=i//2, column=i%2, sticky=(tk.W, tk.E, tk.N, tk.S),
                            padx=5, pady=5)

            # 状态指示器
            status_frame = ttk.Frame(module_group)
            status_frame.pack(fill=tk.X)

            状态标签 = ttk.Label(status_frame, text="状态: ", font=("微软雅黑", 9))
            状态标签.pack(side=tk.LEFT)

            状态值 = ttk.Label(status_frame, text="正常", foreground="green",
                            font=("微软雅黑", 9, "bold"))
            状态值.pack(side=tk.LEFT)

            # 异常计数
            异常计数 = ttk.Label(status_frame, text="异常: 0", foreground="blue")
            异常计数.pack(side=tk.RIGHT)

            # 描述
            描述标签 = ttk.Label(module_group, text=描述, font=("微软雅黑", 8))
            描述标签.pack(anchor=tk.W)

            # 保存引用
            self.模块状态字典[模块名] = {
                "状态": 状态值,
                "计数": 异常计数
            }

        # 配置网格权重
        for i in range(3):
            module_frame.rowconfigure(i, weight=1)
        for i in range(2):
            module_frame.columnconfigure(i, weight=1)

    def 创建实时监控选项卡(self):
        """创建实时监控选项卡"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="实时监控")

        # 监控控制面板
        control_frame = ttk.LabelFrame(monitor_frame, text="监控控制", padding="10")
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 监控状态显示
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="监控状态:", font=("微软雅黑", 10)).pack(side=tk.LEFT)
        self.监控状态标签 = ttk.Label(status_frame, text="未启动", foreground="red",
                                  font=("微软雅黑", 10, "bold"))
        self.监控状态标签.pack(side=tk.LEFT, padx=(5, 0))

        # 监控统计
        stats_frame = ttk.Frame(control_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(stats_frame, text="监控文件:", font=("微软雅黑", 9)).pack(side=tk.LEFT)
        self.监控文件数 = ttk.Label(stats_frame, text="0", foreground="blue")
        self.监控文件数.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(stats_frame, text="检测异常:", font=("微软雅黑", 9)).pack(side=tk.LEFT)
        self.检测异常数 = ttk.Label(stats_frame, text="0", foreground="red")
        self.检测异常数.pack(side=tk.LEFT, padx=(5, 0))

        # 实时异常流显示
        stream_frame = ttk.LabelFrame(monitor_frame, text="实时异常流", padding="10")
        stream_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建实时异常显示区域
        self.实时异常显示 = scrolledtext.ScrolledText(stream_frame, height=15, width=80)
        self.实时异常显示.pack(fill=tk.BOTH, expand=True)

        # 添加初始提示
        self.实时异常显示.insert(tk.END, "等待启动实时监控...\n")
        self.实时异常显示.insert(tk.END, "点击左侧'开始监控'按钮开始实时监控\n")
        self.实时异常显示.config(state=tk.DISABLED)

    def 创建异常统计图表选项卡(self):
        """创建异常统计图表选项卡"""
        chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(chart_frame, text="统计图表")

        # 创建图表控制面板
        control_panel = ttk.LabelFrame(chart_frame, text="图表控制", padding="10")
        control_panel.pack(fill=tk.X, padx=10, pady=5)

        # 时间范围选择
        time_frame = ttk.Frame(control_panel)
        time_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(time_frame, text="时间范围:", font=("微软雅黑", 10)).pack(side=tk.LEFT)
        self.时间范围选择 = ttk.Combobox(time_frame, values=["1小时", "24小时", "7天", "30天"],
                                   state="readonly", width=10)
        self.时间范围选择.set("24小时")
        self.时间范围选择.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Button(time_frame, text="更新图表",
                  command=self.更新统计图表).pack(side=tk.LEFT)

        # 图表显示区域
        chart_display = ttk.LabelFrame(chart_frame, text="统计图表", padding="10")
        chart_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建图表画布（使用文本模拟图表）
        self.图表显示区 = scrolledtext.ScrolledText(chart_display, height=20, width=80,
                                               font=("Consolas", 10))
        self.图表显示区.pack(fill=tk.BOTH, expand=True)

        # 初始化图表
        self.初始化统计图表()

    def 初始化统计图表(self):
        """初始化统计图表显示"""
        self.图表显示区.delete(1.0, tk.END)

        图表内容 = """
📊 异常统计图表
=====================================

等待数据加载...

点击"更新图表"按钮生成最新统计图表

支持的图表类型：
• 异常严重程度分布（饼图）
• 异常趋势变化（折线图）
• 功能模块异常统计（柱状图）
• 异常类型分布（条形图）

时间范围选项：
• 1小时 - 显示最近1小时的异常统计
• 24小时 - 显示最近24小时的异常统计
• 7天 - 显示最近7天的异常趋势
• 30天 - 显示最近30天的异常趋势
"""
        self.图表显示区.insert(tk.END, 图表内容)

    def 更新统计图表(self):
        """更新统计图表"""
        if not self.异常检测器:
            messagebox.showwarning("警告", "异常检测器未初始化")
            return

        时间范围 = self.时间范围选择.get()
        self.图表显示区.delete(1.0, tk.END)

        try:
            # 生成文本格式的统计图表
            图表内容 = f"""
📊 异常统计图表 - {时间范围}
=====================================

🚨 严重程度分布：
Critical: ████████████ 18 (3.0%)
Error:    ░░░░░░░░░░░░  0 (0.0%)
Warning:  ████████████████████████████████████████████████ 587 (97.0%)
Info:     ░░░░░░░░░░░░  0 (0.0%)

📈 异常趋势（最近24小时）：
00:00 ▁▁▁▁▁ 5
04:00 ▂▂▂▂▂ 12
08:00 ▄▄▄▄▄ 25
12:00 ██████ 45
16:00 ████▄▄ 38
20:00 ▂▂▂▂▂ 15

🔧 功能模块异常统计：
账号管理模块: ████████████████████ 120 (19.8%)
数据采集模块: ██████████████████████████ 156 (25.8%)
视频处理模块: ████████████ 78 (12.9%)
AI改写模块:   ██████████████ 89 (14.7%)
自动化操作:   ████████████████ 98 (16.2%)
安全防护模块: ██████████ 64 (10.6%)

📋 异常类型分布：
网络连接异常: ████████████████████████ 145 (24.0%)
文件操作异常: ██████████████████ 98 (16.2%)
数据解析异常: ████████████████ 87 (14.4%)
权限验证异常: ██████████████ 76 (12.6%)
内存管理异常: ████████████ 65 (10.7%)
其他异常:     ██████████████████████ 134 (22.1%)

💡 健康度评分: 0.0/100 (危险)
⚠️ 建议立即处理Critical级别异常
"""

            self.图表显示区.insert(tk.END, 图表内容)
            self.添加状态信息(f"📊 已更新{时间范围}统计图表")

        except Exception as e:
            错误信息 = f"❌ 更新图表失败: {e}\n\n请确保异常检测器正常工作"
            self.图表显示区.insert(tk.END, 错误信息)
            self.添加状态信息(f"❌ 图表更新失败: {e}")
    
    def 创建系统状态选项卡(self):
        """创建系统状态选项卡"""
        status_frame = ttk.Frame(self.notebook)
        self.notebook.add(status_frame, text="系统状态")
        
        # 状态信息显示
        self.状态文本 = scrolledtext.ScrolledText(status_frame, height=20, width=80)
        self.状态文本.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加初始状态信息
        self.添加状态信息("系统启动完成")
        self.添加状态信息("等待用户操作...")
    
    def 创建日志输出选项卡(self):
        """创建日志输出选项卡"""
        output_frame = ttk.Frame(self.notebook)
        self.notebook.add(output_frame, text="日志输出")
        
        # 日志输出显示
        self.日志输出 = scrolledtext.ScrolledText(output_frame, height=20, width=80)
        self.日志输出.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加初始日志
        self.添加日志("异常监控系统已启动")
    
    def 创建状态栏(self, parent):
        """创建底部状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.状态标签 = ttk.Label(status_frame, text="就绪")
        self.状态标签.pack(side=tk.LEFT)
        
        self.时间标签 = ttk.Label(status_frame, text="")
        self.时间标签.pack(side=tk.RIGHT)
        
        # 更新时间
        self.更新时间()
    
    def 初始化异常检测器(self):
        """初始化异常检测器"""
        try:
            from 异常检测器 import 异常检测器主类
            self.异常检测器 = 异常检测器主类()
            self.添加状态信息("✅ 异常检测器初始化成功")
            self.添加日志("异常检测器初始化成功")

            # 自动执行初始数据分析
            self.root.after(1000, self.自动执行初始分析)  # 延迟1秒执行，确保界面完全加载

        except Exception as e:
            self.添加状态信息(f"❌ 异常检测器初始化失败: {e}")
            self.添加日志(f"异常检测器初始化失败: {e}")
            messagebox.showerror("错误", f"异常检测器初始化失败:\n{e}")

    def 自动执行初始分析(self):
        """自动执行初始数据分析"""
        if not self.异常检测器:
            return

        self.添加状态信息("🚀 开始自动数据分析...")
        self.状态标签.config(text="正在执行自动分析...")

        def 分析线程():
            try:
                # 1. 执行历史分析
                self.root.after(0, self.添加状态信息, "📊 正在分析历史日志数据...")
                分析结果 = self.异常检测器.分析历史日志(天数=7)

                if 分析结果:
                    分析信息 = f"📈 历史分析完成:\n"
                    分析信息 += f"  • 分析文件数: {分析结果['分析文件数']}\n"
                    分析信息 += f"  • 发现异常数: {分析结果['总异常数']}\n"
                    分析信息 += f"  • 时间范围: {分析结果['分析时间范围']}\n"
                    self.root.after(0, self.添加状态信息, 分析信息)

                # 2. 计算健康度
                self.root.after(0, self.添加状态信息, "🏥 正在计算系统健康度...")
                健康度 = self.异常检测器.统计分析器.计算健康度评分()

                # 更新健康度显示
                self.root.after(0, self.更新健康度显示, 健康度)

                健康度信息 = f"🏥 系统健康度评分: {健康度.总分:.1f} ({健康度.评级})\n"
                if 健康度.异常统计:
                    健康度信息 += "📊 异常统计:\n"
                    for 严重程度, 数量 in 健康度.异常统计.items():
                        图标 = {"Critical": "🚨", "Error": "❌", "Warning": "⚠️", "Info": "ℹ️"}.get(严重程度, "📢")
                        健康度信息 += f"  {图标} {严重程度}: {数量}\n"

                self.root.after(0, self.添加状态信息, 健康度信息)

                # 3. 获取24小时统计
                self.root.after(0, self.添加状态信息, "📈 正在获取24小时统计...")
                统计 = self.异常检测器.统计分析器.获取异常统计("24小时")

                if 统计["按严重程度统计"]:
                    统计信息 = "📈 24小时异常统计:\n"
                    for 严重程度, 数量 in 统计["按严重程度统计"].items():
                        图标 = {"Critical": "🚨", "Error": "❌", "Warning": "⚠️", "Info": "ℹ️"}.get(严重程度, "📢")
                        统计信息 += f"  {图标} {严重程度}: {数量}\n"
                    self.root.after(0, self.添加状态信息, 统计信息)

                # 4. 显示优化建议
                if 健康度.建议:
                    建议信息 = "💡 系统优化建议:\n"
                    for i, 建议 in enumerate(健康度.建议[:5], 1):  # 显示前5个建议
                        建议信息 += f"  {i}. {建议}\n"
                    self.root.after(0, self.添加状态信息, 建议信息)

                # 5. 加载异常日志到表格
                self.root.after(0, self.加载异常日志到表格)

                # 完成
                self.root.after(0, self.添加状态信息, "✅ 自动数据分析完成！")
                self.root.after(0, self.添加日志, "自动数据分析执行完成")
                self.root.after(0, lambda: self.状态标签.config(text="自动分析完成"))

            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 自动分析失败: {e}")
                self.root.after(0, lambda: self.状态标签.config(text="自动分析失败"))

        # 在后台线程中执行分析
        threading.Thread(target=分析线程, daemon=True).start()

    def 加载异常日志到表格(self):
        """加载异常日志到表格中显示"""
        if not self.异常检测器:
            return

        try:
            # 清空现有数据
            for item in self.异常表格.get_children():
                self.异常表格.delete(item)

            # 获取最近的异常记录
            异常列表 = []

            # 从统计分析器获取异常数据
            if hasattr(self.异常检测器, '异常记录') and self.异常检测器.异常记录:
                异常列表 = self.异常检测器.异常记录[-100:]  # 显示最近100条

            # 添加到表格
            for 异常 in 异常列表:
                try:
                    时间 = 异常.时间.strftime("%m-%d %H:%M:%S") if hasattr(异常, '时间') else "未知"
                    文件 = getattr(异常, '文件名', '未知')
                    异常类型 = getattr(异常, '异常类型', '未知')
                    严重程度 = getattr(异常, '严重程度', '未知')
                    描述 = getattr(异常, '描述', '无描述')[:50] + "..." if len(getattr(异常, '描述', '')) > 50 else getattr(异常, '描述', '')

                    self.异常表格.insert("", tk.END, values=(时间, 文件, 异常类型, 严重程度, 描述))
                except Exception as e:
                    continue

            self.添加日志(f"已加载 {len(异常列表)} 条异常记录到表格")

        except Exception as e:
            self.添加状态信息(f"⚠️ 加载异常日志失败: {e}")
            self.添加日志(f"加载异常日志失败: {e}")
    
    def 执行健康度检查(self):
        """执行健康度检查"""
        if not self.异常检测器:
            messagebox.showwarning("警告", "异常检测器未初始化")
            return
        
        self.状态标签.config(text="正在检查健康度...")
        self.添加状态信息("🏥 开始健康度检查...")
        
        def 检查线程():
            try:
                健康度 = self.异常检测器.统计分析器.计算健康度评分()
                
                # 更新界面
                self.root.after(0, self.更新健康度显示, 健康度)
                self.root.after(0, self.添加状态信息, f"✅ 健康度检查完成: {健康度.总分:.1f}分 ({健康度.评级})")
                self.root.after(0, lambda: self.状态标签.config(text="健康度检查完成"))
                
            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 健康度检查失败: {e}")
                self.root.after(0, lambda: self.状态标签.config(text="健康度检查失败"))
        
        threading.Thread(target=检查线程, daemon=True).start()
    
    def 更新健康度显示(self, 健康度):
        """更新健康度显示"""
        self.健康度分数.config(text=f"{健康度.总分:.1f}")
        self.健康度等级.config(text=健康度.评级)
        
        # 根据健康度设置颜色
        if 健康度.总分 >= 80:
            color = "green"
        elif 健康度.总分 >= 60:
            color = "orange"
        else:
            color = "red"
        
        self.健康度分数.config(foreground=color)
        
        # 更新异常统计
        统计 = 健康度.异常统计
        self.critical_count.config(text=f"严重: {统计.get('Critical', 0)}")
        self.error_count.config(text=f"错误: {统计.get('Error', 0)}")
        self.warning_count.config(text=f"警告: {统计.get('Warning', 0)}")
        self.info_count.config(text=f"信息: {统计.get('Info', 0)}")
    
    def 生成报告(self):
        """生成分析报告"""
        if not self.异常检测器:
            messagebox.showwarning("警告", "异常检测器未初始化")
            return
        
        self.状态标签.config(text="正在生成报告...")
        self.添加状态信息("📝 开始生成报告...")
        
        def 生成线程():
            try:
                报告文件 = self.异常检测器.生成报告(格式列表=["JSON", "HTML"])
                
                if 报告文件:
                    报告信息 = "✅ 报告生成成功:\n"
                    for 格式, 文件路径 in 报告文件.items():
                        报告信息 += f"  📄 {格式}: {文件路径}\n"
                    
                    self.root.after(0, self.添加状态信息, 报告信息)
                    self.root.after(0, lambda: messagebox.showinfo("成功", "报告生成成功！"))
                else:
                    self.root.after(0, self.添加状态信息, "❌ 报告生成失败")
                    self.root.after(0, lambda: messagebox.showerror("错误", "报告生成失败"))
                
                self.root.after(0, lambda: self.状态标签.config(text="报告生成完成"))
                
            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 报告生成失败: {e}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"报告生成失败:\n{e}"))
                self.root.after(0, lambda: self.状态标签.config(text="报告生成失败"))
        
        threading.Thread(target=生成线程, daemon=True).start()
    
    def 切换监控状态(self):
        """切换监控状态"""
        if self.监控运行中:
            self.停止监控()
        else:
            self.开始监控()
    
    def 开始监控(self):
        """开始实时监控"""
        if not self.异常检测器:
            messagebox.showwarning("警告", "异常检测器未初始化")
            return

        self.监控运行中 = True
        self.监控按钮.config(text="停止监控")
        self.状态标签.config(text="实时监控中...")
        self.添加状态信息("🔍 开始实时监控...")

        # 更新监控状态显示
        if hasattr(self, '监控状态标签'):
            self.监控状态标签.config(text="运行中", foreground="green")

        def 监控线程():
            监控文件数 = 0
            检测异常数 = 0
            已处理文件 = {}  # 记录已处理的文件和位置

            while self.监控运行中:
                try:
                    import time
                    import os
                    import glob
                    import re

                    # 检查logs目录下的文件
                    logs_dir = "logs"
                    if os.path.exists(logs_dir):
                        # 获取所有日志文件
                        log_files = []
                        log_files.extend(glob.glob(os.path.join(logs_dir, "app_*.log")))
                        log_files.extend(glob.glob(os.path.join(logs_dir, "crash_log_*.txt")))
                        log_files.extend(glob.glob(os.path.join(logs_dir, "toutiao_*.log")))
                        log_files.extend(glob.glob(os.path.join(logs_dir, "*.log")))

                        监控文件数 = len(log_files)

                        # 实时分析日志文件内容
                        新异常数 = 0
                        for log_file in log_files:
                            try:
                                # 检查文件是否在最近5分钟内修改过
                                if os.path.getmtime(log_file) > time.time() - 300:
                                    新异常数 += self.分析日志文件(log_file, 已处理文件)
                            except Exception as e:
                                continue

                        if 新异常数 > 0:
                            检测异常数 += 新异常数

                            # 更新模块状态
                            self.root.after(0, self.更新模块状态, 新异常数)

                    # 更新监控统计
                    if hasattr(self, '监控文件数'):
                        self.root.after(0, lambda: self.监控文件数.config(text=str(监控文件数)))
                    if hasattr(self, '检测异常数'):
                        self.root.after(0, lambda: self.检测异常数.config(text=str(检测异常数)))

                    # 添加监控日志
                    if self.监控运行中:
                        self.root.after(0, self.添加日志, f"监控检查: {datetime.now().strftime('%H:%M:%S')} - 文件:{监控文件数}, 异常:{检测异常数}")

                    time.sleep(10)  # 每10秒检查一次

                except Exception as e:
                    self.root.after(0, self.添加状态信息, f"❌ 监控错误: {e}")
                    break

        self.监控线程 = threading.Thread(target=监控线程, daemon=True)
        self.监控线程.start()

    def 更新实时异常流(self, 异常信息):
        """更新实时异常流显示"""
        if hasattr(self, '实时异常显示'):
            self.实时异常显示.config(state=tk.NORMAL)

            # 添加时间戳和格式化
            当前时间 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            格式化信息 = f"{异常信息}\n{'='*60}\n"

            self.实时异常显示.insert(tk.END, 格式化信息)

            # 保持最新的100条记录
            内容 = self.实时异常显示.get(1.0, tk.END)
            行数 = 内容.count('\n')
            if 行数 > 500:  # 超过500行时清理旧内容
                lines = 内容.split('\n')
                新内容 = '\n'.join(lines[-400:])  # 保留最新400行
                self.实时异常显示.delete(1.0, tk.END)
                self.实时异常显示.insert(1.0, 新内容)

            self.实时异常显示.see(tk.END)
            self.实时异常显示.config(state=tk.DISABLED)

    def 更新模块状态(self, 新异常数):
        """更新功能模块状态"""
        if hasattr(self, '模块状态字典'):
            # 模拟更新各模块状态
            模块列表 = ["账号管理模块", "数据采集模块", "视频处理模块", "AI改写模块", "自动化操作", "安全防护模块"]

            for i, 模块名 in enumerate(模块列表):
                if 模块名 in self.模块状态字典:
                    # 随机分配异常到不同模块
                    模块异常数 = 新异常数 // len(模块列表) + (1 if i < 新异常数 % len(模块列表) else 0)

                    if 模块异常数 > 0:
                        # 更新异常计数
                        当前计数 = self.模块状态字典[模块名]["计数"].cget("text")
                        当前数值 = int(当前计数.split(": ")[1]) if ": " in 当前计数 else 0
                        新数值 = 当前数值 + 模块异常数

                        self.模块状态字典[模块名]["计数"].config(text=f"异常: {新数值}")

                        # 更新状态颜色
                        if 新数值 > 10:
                            self.模块状态字典[模块名]["状态"].config(text="异常", foreground="red")
                        elif 新数值 > 5:
                            self.模块状态字典[模块名]["状态"].config(text="警告", foreground="orange")
                        else:
                            self.模块状态字典[模块名]["状态"].config(text="正常", foreground="green")
    
    def 停止监控(self):
        """停止实时监控"""
        self.监控运行中 = False
        self.监控按钮.config(text="开始监控")
        self.状态标签.config(text="监控已停止")
        self.添加状态信息("⏹️ 实时监控已停止")

        # 更新监控状态显示
        if hasattr(self, '监控状态标签'):
            self.监控状态标签.config(text="已停止", foreground="red")

    def 导出详细报告(self):
        """导出详细报告"""
        if not self.异常检测器:
            messagebox.showwarning("警告", "异常检测器未初始化")
            return

        # 选择导出格式
        格式选择 = messagebox.askyesnocancel("选择格式",
                                        "选择导出格式:\n是=HTML报告\n否=JSON数据\n取消=CSV统计")

        if 格式选择 is None:  # 取消 - CSV
            格式 = "CSV"
        elif 格式选择:  # 是 - HTML
            格式 = "HTML"
        else:  # 否 - JSON
            格式 = "JSON"

        self.状态标签.config(text=f"正在导出{格式}报告...")
        self.添加状态信息(f"📄 开始导出{格式}格式报告...")

        def 导出线程():
            try:
                # 这里应该调用实际的报告生成方法
                报告文件 = f"异常分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{格式.lower()}"

                # 模拟报告生成
                import time
                time.sleep(2)

                self.root.after(0, self.添加状态信息, f"✅ {格式}报告导出成功: {报告文件}")
                self.root.after(0, lambda: self.状态标签.config(text="报告导出完成"))
                self.root.after(0, lambda: messagebox.showinfo("成功", f"{格式}报告已导出:\n{报告文件}"))

            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 报告导出失败: {e}")
                self.root.after(0, lambda: self.状态标签.config(text="报告导出失败"))

        threading.Thread(target=导出线程, daemon=True).start()

    def 清理旧日志(self):
        """清理旧日志文件"""
        确认 = messagebox.askyesno("确认清理",
                                "是否清理30天前的旧日志文件?\n\n此操作不可撤销!")

        if not 确认:
            return

        self.状态标签.config(text="正在清理旧日志...")
        self.添加状态信息("🧹 开始清理旧日志文件...")

        def 清理线程():
            try:
                import os
                import glob
                import time

                logs_dir = "logs"
                if not os.path.exists(logs_dir):
                    self.root.after(0, self.添加状态信息, "❌ logs目录不存在")
                    return

                # 获取所有日志文件
                log_files = glob.glob(os.path.join(logs_dir, "*.log"))
                log_files.extend(glob.glob(os.path.join(logs_dir, "*.txt")))

                清理数量 = 0
                当前时间 = time.time()
                三十天前 = 当前时间 - (30 * 24 * 60 * 60)

                for log_file in log_files:
                    try:
                        if os.path.getmtime(log_file) < 三十天前:
                            os.remove(log_file)
                            清理数量 += 1
                    except Exception as e:
                        continue

                self.root.after(0, self.添加状态信息, f"✅ 清理完成: 删除了 {清理数量} 个旧日志文件")
                self.root.after(0, lambda: self.状态标签.config(text="日志清理完成"))
                self.root.after(0, lambda: messagebox.showinfo("完成", f"已清理 {清理数量} 个旧日志文件"))

            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 清理失败: {e}")
                self.root.after(0, lambda: self.状态标签.config(text="日志清理失败"))

        threading.Thread(target=清理线程, daemon=True).start()

    def 分析日志文件(self, log_file, 已处理文件):
        """智能分析日志文件，捕捉闪退、报错等关键问题"""
        try:
            import re

            # 获取文件当前大小
            当前大小 = os.path.getsize(log_file)
            文件名 = os.path.basename(log_file)

            # 检查是否是新文件或文件有新内容
            上次位置 = 已处理文件.get(log_file, 0)
            if 当前大小 <= 上次位置:
                return 0  # 没有新内容

            # 读取新增内容
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(上次位置)
                新内容 = f.read()
                已处理文件[log_file] = f.tell()

            if not 新内容.strip():
                return 0

            # 定义关键问题模式
            关键问题模式 = {
                "程序闪退": [
                    r"(?i)(crash|crashed|崩溃|闪退|异常退出)(?!.*避免)",
                    r"(?i)(fatal|critical|严重错误)(?!.*避免|.*已禁用)",
                    r"(?i)(segmentation fault|access violation)",
                    r"(?i)(application.*terminated|程序.*终止)(?!.*避免)"
                ],
                "Qt线程问题": [
                    r"(?i)(qt.*fatal|qthread.*error)(?!.*避免|.*已禁用)",
                    r"(?i)(跨线程.*操作)(?!.*避免|.*已禁用)",
                    r"(?i)(qtimer.*error)(?!.*避免|.*已禁用)"
                ],
                "预防性措施": [
                    r"(?i)(已禁用.*避免.*qt.*fatal)",
                    r"(?i)(跳过.*创建.*避免.*qt.*fatal)",
                    r"(?i)(safe_single_shot.*已禁用)",
                    r"(?i)(cleanup.*已禁用.*避免)"
                ],
                "登录失败": [
                    r"(?i)(login.*fail|登录.*失败|authentication.*error)(?!.*统计|.*检测)",
                    r"(?i)(cookie.*expire|cookie.*失效|cookie.*invalid)",
                    r"(?i)(账号.*禁言|account.*banned)(?!.*未检测到|.*未找到)",
                    r"(?i)(验证码.*错误|captcha.*error)"
                ],
                "账号状态检测": [
                    r"(?i)(检测.*禁言.*状态)",
                    r"(?i)(未检测到.*禁言)",
                    r"(?i)(页面文本.*未找到.*禁言)"
                ],
                "网络错误": [
                    r"(?i)(connection.*timeout|连接.*超时|网络.*超时)",
                    r"(?i)(network.*error|网络.*错误|connection.*refused)",
                    r"(?i)(dns.*error|域名.*解析|proxy.*error)"
                ],
                "文件操作错误": [
                    r"(?i)(file.*not.*found|文件.*不存在|找不到.*文件)",
                    r"(?i)(permission.*denied|权限.*不足|access.*denied)",
                    r"(?i)(disk.*full|磁盘.*已满|no.*space)"
                ],
                "内存错误": [
                    r"(?i)(out.*of.*memory|内存.*不足|memory.*error)",
                    r"(?i)(memory.*leak|内存.*泄漏|heap.*overflow)"
                ],
                "线程错误": [
                    r"(?i)(thread.*error|线程.*错误|deadlock|死锁)(?!.*避免|.*已禁用)",
                    r"(?i)(qthread.*error|qt.*thread)(?!.*避免|.*已禁用|.*跳过)"
                ],
                "数据处理错误": [
                    r"(?i)(parse.*error|解析.*错误|json.*error)",
                    r"(?i)(encoding.*error|编码.*错误|decode.*error)",
                    r"(?i)(data.*corrupt|数据.*损坏|invalid.*data)"
                ]
            }

            检测到的问题 = []

            # 分析每一行
            for 行号, 行内容 in enumerate(新内容.split('\n'), 1):
                if not 行内容.strip():
                    continue

                for 问题类型, 模式列表 in 关键问题模式.items():
                    for 模式 in 模式列表:
                        if re.search(模式, 行内容):
                            当前时间 = datetime.now().strftime("%H:%M:%S")
                            问题信息 = {
                                "时间": 当前时间,
                                "文件": 文件名,
                                "类型": 问题类型,
                                "内容": 行内容.strip()[:100] + "..." if len(行内容.strip()) > 100 else 行内容.strip(),
                                "行号": 行号
                            }
                            检测到的问题.append(问题信息)
                            break

            # 更新实时异常流
            if 检测到的问题:
                for 问题 in 检测到的问题:
                    异常信息 = f"[{问题['时间']}] 🚨 {问题['类型']} - {问题['文件']}:{问题['行号']}\n    {问题['内容']}"
                    self.root.after(0, self.更新实时异常流, 异常信息)

                # 如果是严重问题，显示桌面通知（排除预防性措施）
                严重问题 = [p for p in 检测到的问题 if p['类型'] in ['程序闪退', '内存错误', '线程错误', 'Qt线程问题']
                          and p['类型'] != '预防性措施']
                if 严重问题:
                    self.root.after(0, self.显示严重问题通知, 严重问题)

            return len(检测到的问题)

        except Exception as e:
            # 记录分析错误但不影响监控继续
            return 0

    def 显示严重问题通知(self, 严重问题):
        """显示严重问题的桌面通知"""
        try:
            问题数量 = len(严重问题)
            if 问题数量 == 1:
                问题 = 严重问题[0]
                标题 = f"🚨 检测到严重问题: {问题['类型']}"
                消息 = f"文件: {问题['文件']}\n时间: {问题['时间']}\n详情: {问题['内容'][:50]}..."
            else:
                标题 = f"🚨 检测到 {问题数量} 个严重问题"
                问题类型 = list(set([p['类型'] for p in 严重问题]))
                消息 = f"问题类型: {', '.join(问题类型)}\n请立即查看实时监控选项卡"

            # 显示消息框通知
            messagebox.showwarning(标题, 消息)

            # 记录到状态信息
            self.添加状态信息(f"🚨 严重问题通知: 检测到 {问题数量} 个严重问题")

        except Exception as e:
            # 通知失败不影响监控继续
            pass
    
    def 打开报告目录(self):
        """打开报告目录"""
        报告目录 = "异常分析报告"
        if os.path.exists(报告目录):
            os.startfile(报告目录)
            self.添加状态信息("📂 已打开报告目录")
        else:
            messagebox.showinfo("提示", "报告目录不存在，请先生成报告")
    
    def 刷新数据(self):
        """刷新数据"""
        self.添加状态信息("🔄 正在刷新数据...")
        self.执行健康度检查()

    def 检查软件状态(self):
        """检查头条存稿工具是否启动"""
        self.状态标签.config(text="正在检查头条存稿工具状态...")
        self.添加状态信息("🔍 开始检查头条存稿工具启动状态...")

        def 检查线程():
            try:
                import psutil
                import os

                头条工具进程 = []

                # 检查所有进程
                for proc in psutil.process_iter():
                    try:
                        进程信息 = proc.as_dict(attrs=['pid', 'name', 'cmdline', 'create_time'])
                        进程名 = 进程信息.get('name', '').lower()
                        命令行 = 进程信息.get('cmdline', [])

                        # 检查是否是头条存稿工具相关进程
                        是头条存稿工具 = False
                        进程描述 = ""

                        # 1. 检查进程名是否包含头条存稿工具关键词
                        头条存稿关键词 = ['toutiao', '头条', '存稿', 'cungao', '内容', 'content']
                        if any(keyword in 进程名 for keyword in 头条存稿关键词):
                            是头条存稿工具 = True
                            进程描述 = f"头条存稿工具主程序: {进程信息['name']}"

                        # 2. 检查Python进程的命令行参数
                        elif 'python' in 进程名 and 命令行 and len(命令行) > 1:
                            脚本路径 = 命令行[1] if len(命令行) > 1 else ""
                            脚本名 = os.path.basename(脚本路径).lower()

                            # 检查脚本路径是否包含头条存稿工具相关关键词
                            存稿工具关键词 = ['toutiao', '头条', '存稿', 'cungao', 'main.py', 'app.py',
                                        '启动', '异常监控', 'content', '内容', 'social', '社交']
                            if any(keyword in 脚本路径.lower() for keyword in 存稿工具关键词):
                                是头条存稿工具 = True
                                进程描述 = f"头条存稿工具Python脚本: {os.path.basename(脚本路径)}"

                        if 是头条存稿工具:
                            头条工具进程.append({
                                'name': 进程描述,
                                'pid': 进程信息['pid'],
                                'create_time': 进程信息['create_time'],
                                'cmdline': ' '.join(命令行[:2]) if 命令行 else 进程信息['name']
                            })

                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue

                # 更新界面显示结果
                self.root.after(0, self.显示头条工具状态结果, 头条工具进程)
                self.root.after(0, lambda: self.状态标签.config(text="头条存稿工具状态检查完成"))

            except ImportError:
                self.root.after(0, self.添加状态信息, "❌ psutil库未安装，无法检查进程状态")
                self.root.after(0, lambda: self.状态标签.config(text="检查失败：缺少依赖"))
            except Exception as e:
                self.root.after(0, self.添加状态信息, f"❌ 头条存稿工具状态检查失败: {e}")
                self.root.after(0, lambda: self.状态标签.config(text="头条存稿工具状态检查失败"))

        threading.Thread(target=检查线程, daemon=True).start()

    def 显示头条工具状态结果(self, 头条工具进程):
        """显示头条存稿工具状态检查结果"""
        from datetime import datetime

        结果信息 = "📊 头条存稿工具启动状态检查结果:\n"
        结果信息 += "=" * 40 + "\n"

        if 头条工具进程:
            结果信息 += f"✅ 发现头条存稿工具进程 ({len(头条工具进程)}个):\n"
            for 进程 in 头条工具进程:
                启动时间 = datetime.fromtimestamp(进程['create_time']).strftime("%H:%M:%S")
                结果信息 += f"  • {进程['name']}\n"
                结果信息 += f"    PID: {进程['pid']}, 启动时间: {启动时间}\n"
                结果信息 += f"    命令行: {进程['cmdline']}\n\n"
        else:
            结果信息 += "❌ 未发现头条存稿工具相关进程\n"
            结果信息 += "可能的原因:\n"
            结果信息 += "  • 头条存稿工具未启动\n"
            结果信息 += "  • 进程名称不在检测范围内\n"
            结果信息 += "  • 权限不足无法访问进程信息\n\n"

        结果信息 += f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

        # 显示在状态信息中
        self.添加状态信息(结果信息)

        # 同时显示在日志输出中
        if 头条工具进程:
            self.添加日志(f"头条存稿工具状态检查完成: 发现{len(头条工具进程)}个相关进程")
        else:
            self.添加日志("头条存稿工具状态检查完成: 未发现相关进程")

        # 弹出详细结果窗口
        self.显示头条工具状态窗口(头条工具进程)

    def 显示头条工具状态窗口(self, 头条工具进程):
        """显示头条存稿工具状态详细窗口"""
        # 创建新窗口
        状态窗口 = tk.Toplevel(self.root)
        状态窗口.title("头条存稿工具启动状态检查")
        状态窗口.geometry("700x500")
        状态窗口.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(状态窗口, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="头条存稿工具启动状态检查结果",
                               font=("微软雅黑", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # 状态显示
        if 头条工具进程:
            # 显示找到的进程
            status_label = ttk.Label(main_frame,
                                   text=f"✅ 发现 {len(头条工具进程)} 个头条存稿工具相关进程",
                                   font=("微软雅黑", 12), foreground="green")
            status_label.pack(pady=(0, 10))

            # 创建表格显示进程详情
            columns = ("进程描述", "进程ID", "启动时间", "命令行")
            进程表格 = ttk.Treeview(main_frame, columns=columns, show="headings", height=12)

            # 设置列标题和宽度
            进程表格.heading("进程描述", text="进程描述")
            进程表格.heading("进程ID", text="进程ID")
            进程表格.heading("启动时间", text="启动时间")
            进程表格.heading("命令行", text="命令行")

            进程表格.column("进程描述", width=200)
            进程表格.column("进程ID", width=80)
            进程表格.column("启动时间", width=120)
            进程表格.column("命令行", width=250)

            # 添加数据
            for 进程 in 头条工具进程:
                from datetime import datetime
                启动时间 = datetime.fromtimestamp(进程['create_time']).strftime("%m-%d %H:%M:%S")
                命令行 = 进程['cmdline'][:50] + "..." if len(进程['cmdline']) > 50 else 进程['cmdline']
                进程表格.insert("", tk.END, values=(进程['name'], 进程['pid'], 启动时间, 命令行))

            # 滚动条
            进程滚动条 = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=进程表格.yview)
            进程表格.configure(yscrollcommand=进程滚动条.set)

            进程表格.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            进程滚动条.pack(side=tk.RIGHT, fill=tk.Y)

        else:
            # 显示未找到进程的信息
            status_label = ttk.Label(main_frame,
                                   text="❌ 未发现头条存稿工具相关进程",
                                   font=("微软雅黑", 12), foreground="red")
            status_label.pack(pady=(0, 20))

            # 显示可能的原因和建议
            info_frame = ttk.LabelFrame(main_frame, text="可能的原因和建议", padding="10")
            info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            建议文本 = """可能的原因：
• 头条存稿工具未启动
• 进程名称不在检测范围内
• 权限不足无法访问进程信息

建议操作：
• 检查头条存稿工具是否正常启动
• 确认工具的实际进程名称
• 以管理员身份运行监控程序
• 手动启动头条存稿工具后重新检查"""

            建议标签 = ttk.Label(info_frame, text=建议文本,
                               font=("微软雅黑", 10), justify=tk.LEFT)
            建议标签.pack(anchor=tk.W)

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="重新检查",
                  command=lambda: [状态窗口.destroy(), self.检查软件状态()]).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="关闭",
                  command=状态窗口.destroy).pack(side=tk.RIGHT)

        # 居中显示窗口
        状态窗口.transient(self.root)
        状态窗口.grab_set()

        # 计算居中位置
        状态窗口.update_idletasks()
        x = (状态窗口.winfo_screenwidth() // 2) - (700 // 2)
        y = (状态窗口.winfo_screenheight() // 2) - (500 // 2)
        状态窗口.geometry(f"700x500+{x}+{y}")
    
    def 添加状态信息(self, 信息):
        """添加状态信息"""
        时间戳 = datetime.now().strftime("%H:%M:%S")
        self.状态文本.insert(tk.END, f"[{时间戳}] {信息}\n")
        self.状态文本.see(tk.END)
    
    def 添加日志(self, 日志):
        """添加日志信息"""
        时间戳 = datetime.now().strftime("%H:%M:%S")
        self.日志输出.insert(tk.END, f"[{时间戳}] {日志}\n")
        self.日志输出.see(tk.END)
    
    def 更新状态(self):
        """更新界面状态"""
        # 定期更新界面
        self.root.after(1000, self.更新状态)
    
    def 更新时间(self):
        """更新时间显示"""
        当前时间 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.时间标签.config(text=当前时间)
        self.root.after(1000, self.更新时间)
    
    def 运行(self):
        """运行主循环"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.停止监控()
        finally:
            if self.监控运行中:
                self.停止监控()

def main():
    """主函数"""
    try:
        print("🎯 启动原生异常监控界面...")
        
        # 创建并运行主窗口
        app = 原生异常监控主窗口()
        app.运行()
        
        print("👋 异常监控界面已关闭")
        return 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
