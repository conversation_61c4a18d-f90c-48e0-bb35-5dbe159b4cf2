@echo off
chcp 65001 >nul
echo 🔍 头条内容社交工具 - 异常监控系统
echo =====================================
echo.

REM 检查虚拟环境是否存在
if not exist "venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在，请先创建虚拟环境
    echo 创建命令: python -m venv venv
    pause
    exit /b 1
)

echo ✅ 检测到虚拟环境
echo.

REM 显示菜单
:menu
echo 请选择启动模式:
echo [1] GUI界面模式
echo [2] 命令行 - 健康度检查
echo [3] 命令行 - 生成报告
echo [4] 命令行 - 实时监控
echo [5] 命令行 - 帮助信息
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto health
if "%choice%"=="3" goto report
if "%choice%"=="4" goto monitor
if "%choice%"=="5" goto help
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:gui
echo.
echo 🖥️ 启动GUI界面...
venv\Scripts\python.exe 启动异常监控.py
goto menu

:health
echo.
echo 🏥 执行健康度检查...
venv\Scripts\python.exe 异常检测器.py --health
echo.
pause
goto menu

:report
echo.
echo 📝 生成分析报告...
venv\Scripts\python.exe 异常检测器.py --report --format HTML JSON CSV
echo.
pause
goto menu

:monitor
echo.
echo 🔍 启动实时监控 (按Ctrl+C停止)...
venv\Scripts\python.exe 异常检测器.py --monitor
echo.
pause
goto menu

:help
echo.
echo 📚 异常检测器帮助信息:
venv\Scripts\python.exe 异常检测器.py --help
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用异常监控系统！
pause
exit /b 0
