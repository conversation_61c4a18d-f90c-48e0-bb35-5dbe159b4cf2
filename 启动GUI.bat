@echo off
REM 头条内容社交工具 - GUI启动脚本 (批处理版本)
REM 解决中文编码问题

REM 设置控制台编码为UTF-8
chcp 65001 >nul

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8

echo 🎯 头条内容社交工具 - GUI启动脚本
echo ==================================================

REM 检查虚拟环境
if not exist "venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在
    echo 请先创建虚拟环境: python -m venv venv
    pause
    exit /b 1
)

echo ✅ 找到虚拟环境: venv\Scripts\python.exe

REM 检查关键文件
echo.
echo 📁 检查关键文件:
if exist "异常监控界面.py" (
    echo   ✅ 异常监控界面.py
) else (
    echo   ❌ 异常监控界面.py (缺失)
    goto :error
)

if exist "异常检测器.py" (
    echo   ✅ 异常检测器.py
) else (
    echo   ❌ 异常检测器.py (缺失)
    goto :error
)

if exist "异常检测配置.json" (
    echo   ✅ 异常检测配置.json
) else (
    echo   ❌ 异常检测配置.json (缺失)
    goto :error
)

REM 启动GUI
echo.
echo 🖥️ 启动GUI界面...
echo 提示: 如果出现编码错误，请确保控制台支持UTF-8

REM 尝试不同的启动方式
if exist "简单启动器.py" (
    echo 使用简单启动器...
    venv\Scripts\python.exe 简单启动器.py
    goto :success
)

if exist "启动GUI_编码修复.py" (
    echo 使用编码修复启动器...
    venv\Scripts\python.exe 启动GUI_编码修复.py
    goto :success
)

echo 使用标准启动器...
venv\Scripts\python.exe 启动异常监控.py
goto :success

:error
echo.
echo ❌ 缺失关键文件，无法启动
echo 请确保所有项目文件完整
pause
exit /b 1

:success
echo.
echo 🎉 GUI启动完成
pause
exit /b 0
