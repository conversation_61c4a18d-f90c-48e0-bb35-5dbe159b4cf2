#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条内容社交工具 - 原生异常监控启动器
==================================

使用tkinter原生GUI，无需安装PyQt5等第三方依赖。

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import argparse

def 检查依赖():
    """检查必要的依赖库"""
    缺失依赖 = []
    
    # 检查tkinter（Python内置）
    try:
        import tkinter
        print("✅ tkinter 已安装 (Python内置)")
    except ImportError:
        缺失依赖.append("tkinter")
        print("❌ tkinter 未安装")
    
    # 检查可选依赖
    try:
        import watchdog
        print("✅ watchdog 已安装")
    except ImportError:
        print("⚠️ watchdog 未安装 (实时监控功能将受限)")
    
    try:
        import psutil
        print("✅ psutil 已安装")
    except ImportError:
        print("⚠️ psutil 未安装 (系统信息功能将受限)")
    
    if 缺失依赖:
        print("\n❌ 缺失以下必需依赖库:")
        for 依赖 in 缺失依赖:
            print(f"   - {依赖}")
        return False
    
    return True

def 检查配置文件():
    """检查配置文件"""
    配置文件 = "异常检测配置.json"
    if os.path.exists(配置文件):
        print(f"✅ 配置文件存在: {配置文件}")
        return True
    else:
        print(f"⚠️ 配置文件不存在: {配置文件}")
        print("将使用默认配置")
        return True  # 允许使用默认配置

def 检查核心文件():
    """检查核心文件"""
    核心文件 = [
        "异常检测器.py",
        "原生异常监控界面.py"
    ]
    
    缺失文件 = []
    for 文件 in 核心文件:
        if os.path.exists(文件):
            print(f"✅ {文件}")
        else:
            print(f"❌ {文件} (缺失)")
            缺失文件.append(文件)
    
    if 缺失文件:
        print(f"\n❌ 缺失核心文件: {', '.join(缺失文件)}")
        return False
    
    return True

def 启动原生GUI():
    """启动原生GUI界面"""
    print("🖥️ 启动原生GUI界面...")
    
    try:
        from 原生异常监控界面 import main as gui_main
        return gui_main()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保 原生异常监控界面.py 文件存在")
        return 1
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return 1

def 启动命令行模式(args):
    """启动命令行模式"""
    print("💻 启动命令行模式...")
    
    try:
        from 异常检测器 import 主函数 as cli_main
        
        # 重新设置sys.argv以传递参数给命令行模式
        原始argv = sys.argv.copy()
        sys.argv = ["异常检测器.py"] + args
        
        try:
            结果 = cli_main()
            return 结果
        finally:
            sys.argv = 原始argv
            
    except ImportError as e:
        print(f"❌ 导入命令行模块失败: {e}")
        print("请确保异常检测器.py文件存在")
        return 1
    except Exception as e:
        print(f"❌ 启动命令行模式失败: {e}")
        return 1

def 显示帮助():
    """显示帮助信息"""
    print("""
🎯 头条内容社交工具 - 原生异常监控系统
=====================================

使用方法:
  python 启动原生异常监控.py                    # 启动原生GUI界面
  python 启动原生异常监控.py --cli              # 启动命令行模式
  python 启动原生异常监控.py --cli --help       # 查看命令行参数

原生GUI特点:
  ✅ 无需安装PyQt5等第三方依赖
  ✅ 使用Python内置tkinter库
  ✅ 完整的异常监控功能
  ✅ 中文界面支持
  ✅ 实时监控和报告生成

GUI功能:
  🏥 系统健康度监控
  📊 异常统计显示
  📝 异常日志表格
  🔍 实时监控
  📄 报告生成和导出
  📂 报告目录管理

命令行功能:
  ✅ 实时监控 (--monitor)
  ✅ 历史分析 (--analyze [天数])
  ✅ 生成报告 (--report)
  ✅ 健康度检查 (--health)
  ✅ 统计信息 (--stats [时间范围])

示例:
  python 启动原生异常监控.py                     # 启动GUI
  python 启动原生异常监控.py --cli --monitor     # 命令行监控
  python 启动原生异常监控.py --cli --health      # 健康度检查

优势:
  🚀 启动速度快
  💾 内存占用低
  🔧 无依赖问题
  🎨 原生界面风格
""")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="原生异常监控系统启动器", add_help=False)
    parser.add_argument("--cli", action="store_true", help="启动命令行模式")
    parser.add_argument("--help", "-h", action="store_true", help="显示帮助信息")
    
    # 解析已知参数
    已知参数, 未知参数 = parser.parse_known_args()
    
    # 显示帮助
    if 已知参数.help:
        显示帮助()
        return 0
    
    print("🎯 头条内容社交工具 - 原生异常监控系统")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查系统环境...")
    if not 检查依赖():
        print("\n❌ 依赖检查失败")
        return 1
    
    # 检查配置文件
    检查配置文件()
    
    # 检查核心文件
    if not 检查核心文件():
        print("\n❌ 核心文件检查失败")
        return 1
    
    print("\n✅ 环境检查通过")
    print("=" * 50)
    
    # 根据参数选择启动模式
    if 已知参数.cli:
        return 启动命令行模式(未知参数)
    else:
        return 启动原生GUI()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
