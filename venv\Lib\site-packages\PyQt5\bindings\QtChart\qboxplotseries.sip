// qboxplotseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_3_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qboxplotseries.h>
%End

    class QBoxPlotSeries : public QtCharts::QAbstractSeries
    {
%TypeHeaderCode
#include <qboxplotseries.h>
%End

    public:
        explicit QBoxPlotSeries(QObject *parent /TransferThis/ = 0);
        virtual ~QBoxPlotSeries();
        bool append(QtCharts::QBoxSet *box /Transfer/);
        bool remove(QtCharts::QBoxSet *box);
        bool take(QtCharts::QBoxSet *box);
        bool append(QList<QtCharts::QBoxSet *> boxes /Transfer/);
        bool insert(int index, QtCharts::QBoxSet *box /Transfer/);
        int count() const /__len__/;
        QList<QtCharts::QBoxSet *> boxSets() const;
        void clear();
        virtual QtCharts::QAbstractSeries::SeriesType type() const;
        void setBoxOutlineVisible(bool visible);
        bool boxOutlineVisible();
        void setBoxWidth(qreal width);
        qreal boxWidth();
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setPen(const QPen &pen);
        QPen pen() const;

    signals:
        void clicked(QtCharts::QBoxSet *boxset /ScopesStripped=1/);
        void hovered(bool status, QtCharts::QBoxSet *boxset /ScopesStripped=1/);
        void countChanged();
        void penChanged();
        void brushChanged();
        void boxOutlineVisibilityChanged();
        void boxWidthChanged();
        void boxsetsAdded(QList<QtCharts::QBoxSet *> sets /ScopesStripped=1/);
        void boxsetsRemoved(QList<QtCharts::QBoxSet *> sets /ScopesStripped=1/);
%If (QtChart_2_0_0 -)
        void pressed(QtCharts::QBoxSet *boxset /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void released(QtCharts::QBoxSet *boxset /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(QtCharts::QBoxSet *boxset /ScopesStripped=1/);
%End
    };
};

%End
