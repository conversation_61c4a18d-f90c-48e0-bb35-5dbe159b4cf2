# 🔧 虚拟环境使用指南

## 📋 环境状态

✅ **虚拟环境配置完成**
- Python版本: 3.9.13 ✅
- 虚拟环境: venv ✅
- 依赖库: 全部安装 ✅
- 项目文件: 完整 ✅
- 日志目录: 263个文件 ✅

## 🚀 启动方式

### 方式1: 直接启动 (推荐)

```bash
# GUI界面
venv\Scripts\python.exe 启动异常监控.py

# 命令行模式
venv\Scripts\python.exe 异常检测器.py --help
```

### 方式2: 使用启动器

```bash
# 虚拟环境启动器 (交互式菜单)
venv\Scripts\python.exe 虚拟环境启动器.py

# 批处理文件启动器
启动异常监控_虚拟环境.bat

# PowerShell启动器
.\启动异常监控_虚拟环境.ps1
```

## 🔧 常用命令

### 健康度检查
```bash
venv\Scripts\python.exe 异常检测器.py --health
```

### 生成分析报告
```bash
venv\Scripts\python.exe 异常检测器.py --report --format HTML JSON CSV
```

### 实时监控
```bash
venv\Scripts\python.exe 异常检测器.py --monitor
```

### 查看统计信息
```bash
venv\Scripts\python.exe 异常检测器.py --stats 24小时
```

### 历史分析
```bash
venv\Scripts\python.exe 异常检测器.py --analyze 7
```

## 📊 当前系统状态

根据最新检测结果：

- **📁 分析文件数**: 263个日志文件
- **🔍 发现异常总数**: 605个异常
- **🚨 严重异常**: 18个 (Critical级别)
- **⚠️ 警告异常**: 587个 (Warning级别)
- **📊 系统健康度**: 0.0分 (危险级别)

### 主要问题分析

1. **💥 崩溃日志较多**: 185个崩溃日志文件
2. **🔍 账号管理异常**: 大量账号相关问题
3. **⚠️ 系统稳定性**: 需要重点关注Critical异常

## 🎯 使用建议

### 1. 立即处理严重问题
```bash
# 查看详细健康度报告
venv\Scripts\python.exe 异常检测器.py --health

# 生成完整分析报告
venv\Scripts\python.exe 异常检测器.py --report
```

### 2. 启动实时监控
```bash
# 后台监控模式
venv\Scripts\python.exe 异常检测器.py --monitor
```

### 3. 定期生成报告
建议每天生成一次分析报告，跟踪系统健康度变化。

## 📁 重要文件说明

| 文件名 | 用途 | 大小 |
|--------|------|------|
| `异常检测配置.json` | 系统配置文件 | 7.3 KB |
| `异常检测器.py` | 核心检测引擎 | 42.9 KB |
| `异常监控界面.py` | GUI界面 | 44.5 KB |
| `启动异常监控.py` | 统一启动器 | 4.8 KB |
| `虚拟环境启动器.py` | 虚拟环境专用启动器 | 8.9 KB |
| `检查虚拟环境.py` | 环境检查工具 | 12.1 KB |

## 🔍 故障排除

### 问题1: 编码错误
如果遇到编码问题，在命令行中设置编码：
```bash
chcp 65001
set PYTHONIOENCODING=utf-8
venv\Scripts\python.exe 异常检测器.py --health
```

### 问题2: GUI无法启动
检查PyQt5是否正确安装：
```bash
venv\Scripts\python.exe -c "import PyQt5; print('PyQt5 OK')"
```

### 问题3: 实时监控失败
检查watchdog库：
```bash
venv\Scripts\python.exe -c "import watchdog; print('watchdog OK')"
```

### 问题4: 权限问题
确保对以下目录有读写权限：
- `logs/` - 日志文件目录
- `异常分析报告/` - 报告输出目录

## 📈 性能优化建议

1. **定期清理日志**: 删除过期的日志文件
2. **调整监控频率**: 修改配置文件中的监控间隔
3. **优化异常模式**: 根据实际情况调整检测规则

## 🎉 成功案例

系统已成功检测到：
- ✅ 程序崩溃问题 (18个Critical异常)
- ✅ 账号管理问题 (587个Warning异常)
- ✅ 系统稳定性问题
- ✅ 生成详细分析报告

## 📞 技术支持

如遇到问题：
1. 查看 `异常检测器.log` 获取详细错误信息
2. 运行 `检查虚拟环境.py` 验证配置
3. 查看 `异常检测系统使用说明.md` 获取更多帮助

## 🔮 下一步操作

1. **立即启动GUI界面**查看实时状态
2. **生成HTML报告**进行详细分析
3. **配置实时监控**实现持续监控
4. **根据报告建议**优化系统稳定性

---

**🎯 异常检测系统已在虚拟环境中完全配置完成，可以立即投入使用！**

*配置完成时间: 2025-08-01*  
*虚拟环境: venv (Python 3.9.13)*
