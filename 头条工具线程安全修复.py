#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
头条工具线程安全修复示例
解决Qt Fatal错误和线程安全问题
"""

import threading
import time
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer, QApplication, QMetaObject, Qt
from PyQt5.QtCore import Q_ARG
from PyQt5.QtWidgets import QWidget

class 线程安全管理器(QObject):
    """线程安全管理器 - 解决Qt线程问题"""
    
    # 定义信号用于线程间通信
    update_ui_signal = pyqtSignal(str, str)  # (控件名, 文本)
    create_timer_signal = pyqtSignal(int, object)  # (延迟, 回调函数)
    show_message_signal = pyqtSignal(str, str)  # (标题, 消息)
    update_progress_signal = pyqtSignal(int)  # 进度更新
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.threads = []
        self.timers = []
        self.cleanup_enabled = True
        
        # 连接信号到槽函数
        self.update_ui_signal.connect(self.安全更新界面)
        self.create_timer_signal.connect(self.安全创建定时器)
        self.show_message_signal.connect(self.安全显示消息)
    
    def 是否在主线程(self):
        """检查当前是否在主线程"""
        return QThread.currentThread() == QApplication.instance().thread()
    
    def 安全更新界面(self, 控件名, 文本):
        """线程安全的界面更新"""
        try:
            if hasattr(self.main_window, 控件名):
                控件 = getattr(self.main_window, 控件名)
                if hasattr(控件, 'setText'):
                    控件.setText(文本)
                elif hasattr(控件, 'setPlainText'):
                    控件.setPlainText(文本)
        except Exception as e:
            print(f"界面更新错误: {e}")
    
    def 安全创建定时器(self, 延迟, 回调):
        """线程安全的定时器创建"""
        try:
            if self.是否在主线程():
                timer = QTimer()
                timer.setSingleShot(True)
                timer.timeout.connect(回调)
                timer.start(延迟)
                self.timers.append(timer)
            else:
                print("警告: 尝试在非主线程中创建定时器")
        except Exception as e:
            print(f"定时器创建错误: {e}")
    
    def 安全显示消息(self, 标题, 消息):
        """线程安全的消息显示"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            if self.是否在主线程():
                QMessageBox.information(self.main_window, 标题, 消息)
            else:
                print(f"消息: {标题} - {消息}")
        except Exception as e:
            print(f"消息显示错误: {e}")
    
    def 创建安全线程(self, target, *args, **kwargs):
        """创建线程安全的工作线程"""
        def 包装函数():
            try:
                target(*args, **kwargs)
            except Exception as e:
                # 使用信号报告错误
                self.show_message_signal.emit("线程错误", str(e))
        
        thread = threading.Thread(target=包装函数, daemon=True)
        self.threads.append(thread)
        return thread
    
    def 安全单次调用(self, 延迟, 回调):
        """安全的单次延迟调用"""
        if self.是否在主线程():
            # 在主线程中使用QTimer
            self.create_timer_signal.emit(延迟, 回调)
        else:
            # 在工作线程中使用Python定时器
            def 延迟执行():
                time.sleep(延迟 / 1000.0)  # 转换为秒
                try:
                    回调()
                except Exception as e:
                    print(f"回调执行错误: {e}")
            
            thread = threading.Thread(target=延迟执行, daemon=True)
            thread.start()
    
    def 清理资源(self):
        """安全清理所有资源"""
        if not self.cleanup_enabled:
            return
        
        try:
            # 停止所有定时器
            for timer in self.timers:
                if timer.isActive():
                    timer.stop()
            self.timers.clear()
            
            # 等待线程完成
            for thread in self.threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            self.threads.clear()
            
        except Exception as e:
            print(f"资源清理警告: {e}")
    
    def 禁用清理(self):
        """禁用清理以避免Qt Fatal错误"""
        self.cleanup_enabled = False

class 批量存稿线程安全版(QObject):
    """批量存稿的线程安全版本"""
    
    # 定义信号
    status_update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # (当前, 总数)
    result_signal = pyqtSignal(dict)  # 结果统计
    error_signal = pyqtSignal(str)
    
    def __init__(self, 线程管理器):
        super().__init__()
        self.线程管理器 = 线程管理器
        self.停止标志 = False
    
    def 开始批量存稿(self, 账号列表):
        """开始批量存稿任务"""
        # 创建工作线程
        工作线程 = self.线程管理器.创建安全线程(self.执行批量存稿, 账号列表)
        工作线程.start()
    
    def 执行批量存稿(self, 账号列表):
        """在工作线程中执行批量存稿"""
        try:
            总数 = len(账号列表)
            成功数 = 0
            失败数 = 0
            
            for i, 账号 in enumerate(账号列表):
                if self.停止标志:
                    break
                
                # 更新状态
                self.status_update_signal.emit(f"正在处理账号: {账号}")
                self.progress_signal.emit(i + 1, 总数)
                
                # 执行存稿操作
                try:
                    结果 = self.处理单个账号(账号)
                    if 结果:
                        成功数 += 1
                    else:
                        失败数 += 1
                except Exception as e:
                    失败数 += 1
                    print(f"账号 {账号} 处理失败: {e}")
                
                # 避免过快执行
                time.sleep(0.1)
            
            # 发送结果
            结果统计 = {
                "总数": 总数,
                "成功": 成功数,
                "失败": 失败数
            }
            self.result_signal.emit(结果统计)
            
        except Exception as e:
            self.error_signal.emit(f"批量存稿错误: {e}")
    
    def 处理单个账号(self, 账号):
        """处理单个账号的存稿"""
        # 这里是实际的存稿逻辑
        # 注意：不要在这里直接操作Qt控件
        try:
            # 模拟存稿操作
            time.sleep(1)
            return True
        except Exception as e:
            print(f"账号 {账号} 存稿失败: {e}")
            return False
    
    def 停止任务(self):
        """停止批量存稿任务"""
        self.停止标志 = True

class 头条工具主窗口修复版(QWidget):
    """头条工具主窗口的线程安全修复版"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化线程安全管理器
        self.线程管理器 = 线程安全管理器(self)
        
        # 初始化批量存稿
        self.批量存稿 = 批量存稿线程安全版(self.线程管理器)
        
        # 连接信号
        self.批量存稿.status_update_signal.connect(self.更新状态显示)
        self.批量存稿.progress_signal.connect(self.更新进度显示)
        self.批量存稿.result_signal.connect(self.显示结果)
        self.批量存稿.error_signal.connect(self.显示错误)
        
        self.初始化界面()
    
    def 初始化界面(self):
        """初始化界面"""
        # 这里添加界面初始化代码
        pass
    
    def 更新状态显示(self, 状态文本):
        """更新状态显示"""
        # 这个方法在主线程中执行，可以安全操作Qt控件
        print(f"状态更新: {状态文本}")
    
    def 更新进度显示(self, 当前, 总数):
        """更新进度显示"""
        进度百分比 = int((当前 / 总数) * 100)
        print(f"进度: {当前}/{总数} ({进度百分比}%)")
    
    def 显示结果(self, 结果统计):
        """显示结果统计"""
        消息 = f"批量存稿完成:\n总数: {结果统计['总数']}\n成功: {结果统计['成功']}\n失败: {结果统计['失败']}"
        self.线程管理器.show_message_signal.emit("完成", 消息)
    
    def 显示错误(self, 错误消息):
        """显示错误消息"""
        self.线程管理器.show_message_signal.emit("错误", 错误消息)
    
    def 安全延迟调用(self, 延迟, 回调):
        """安全的延迟调用"""
        self.线程管理器.安全单次调用(延迟, 回调)
    
    def 安全更新界面文本(self, 控件名, 文本):
        """安全更新界面文本"""
        if self.线程管理器.是否在主线程():
            # 直接更新
            self.线程管理器.安全更新界面(控件名, 文本)
        else:
            # 通过信号更新
            self.线程管理器.update_ui_signal.emit(控件名, 文本)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 安全清理资源
        self.线程管理器.禁用清理()  # 避免Qt Fatal错误
        self.批量存稿.停止任务()
        
        # 等待一小段时间让线程完成
        QApplication.processEvents()
        time.sleep(0.1)
        
        event.accept()

# 使用示例
def 使用示例():
    """使用示例"""
    app = QApplication([])
    
    # 创建主窗口
    主窗口 = 头条工具主窗口修复版()
    
    # 开始批量存稿
    账号列表 = ["账号1", "账号2", "账号3"]
    主窗口.批量存稿.开始批量存稿(账号列表)
    
    # 安全延迟调用示例
    def 延迟回调():
        print("延迟回调执行")
    
    主窗口.安全延迟调用(5000, 延迟回调)  # 5秒后执行
    
    主窗口.show()
    app.exec_()

if __name__ == "__main__":
    使用示例()
