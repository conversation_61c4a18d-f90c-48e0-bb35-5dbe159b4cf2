#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Exception Monitor - English GUI Launcher
========================================

English version GUI launcher to avoid encoding issues.

Author: AI Assistant
Version: 1.0.0
"""

import sys
import os

def setup_environment():
    """Setup encoding environment"""
    try:
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        return True
    except Exception as e:
        print(f"Warning: Failed to setup environment: {e}")
        return False

def create_english_gui():
    """Create English version GUI"""
    try:
        from PyQt5.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
            QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QLabel,
            QPushButton, QComboBox, QGroupBox, QSplitter, QProgressBar,
            QHeaderView, QFrame, QGridLayout, QMessageBox
        )
        from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
        from PyQt5.QtGui import QFont, QColor
        
        print("✅ PyQt5 imported successfully")
        
        # Import the detector
        from 异常检测器 import 异常检测器主类
        
        class ExceptionMonitorWindow(QMainWindow):
            """Main Exception Monitor Window"""
            
            def __init__(self):
                super().__init__()
                self.detector = None
                self.init_ui()
                self.init_detector()
            
            def init_ui(self):
                """Initialize UI"""
                self.setWindowTitle("Exception Monitor System")
                self.setGeometry(100, 100, 1200, 800)
                
                # Central widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                # Main layout
                main_layout = QVBoxLayout(central_widget)
                
                # Title
                title = QLabel("Exception Monitor System")
                title.setAlignment(Qt.AlignCenter)
                title.setFont(QFont("Arial", 16, QFont.Bold))
                main_layout.addWidget(title)
                
                # Control buttons
                button_layout = QHBoxLayout()
                
                self.health_btn = QPushButton("Health Check")
                self.health_btn.clicked.connect(self.check_health)
                button_layout.addWidget(self.health_btn)
                
                self.report_btn = QPushButton("Generate Report")
                self.report_btn.clicked.connect(self.generate_report)
                button_layout.addWidget(self.report_btn)
                
                self.monitor_btn = QPushButton("Start Monitor")
                self.monitor_btn.clicked.connect(self.start_monitor)
                button_layout.addWidget(self.monitor_btn)
                
                button_layout.addStretch()
                main_layout.addLayout(button_layout)
                
                # Status display
                self.status_text = QTextEdit()
                self.status_text.setMaximumHeight(200)
                self.status_text.setReadOnly(True)
                main_layout.addWidget(self.status_text)
                
                # Health score display
                health_group = QGroupBox("System Health")
                health_layout = QGridLayout()
                
                self.health_score = QLabel("Score: --")
                self.health_score.setFont(QFont("Arial", 14, QFont.Bold))
                health_layout.addWidget(QLabel("Health Score:"), 0, 0)
                health_layout.addWidget(self.health_score, 0, 1)
                
                self.health_level = QLabel("Level: --")
                health_layout.addWidget(QLabel("Health Level:"), 1, 0)
                health_layout.addWidget(self.health_level, 1, 1)
                
                health_group.setLayout(health_layout)
                main_layout.addWidget(health_group)
                
                # Statistics display
                stats_group = QGroupBox("Exception Statistics")
                stats_layout = QGridLayout()
                
                self.critical_count = QLabel("0")
                self.error_count = QLabel("0")
                self.warning_count = QLabel("0")
                self.info_count = QLabel("0")
                
                stats_layout.addWidget(QLabel("Critical:"), 0, 0)
                stats_layout.addWidget(self.critical_count, 0, 1)
                stats_layout.addWidget(QLabel("Error:"), 0, 2)
                stats_layout.addWidget(self.error_count, 0, 3)
                stats_layout.addWidget(QLabel("Warning:"), 1, 0)
                stats_layout.addWidget(self.warning_count, 1, 1)
                stats_layout.addWidget(QLabel("Info:"), 1, 2)
                stats_layout.addWidget(self.info_count, 1, 3)
                
                stats_group.setLayout(stats_layout)
                main_layout.addWidget(stats_group)
                
                self.log_message("Exception Monitor System initialized")
            
            def init_detector(self):
                """Initialize exception detector"""
                try:
                    self.detector = 异常检测器主类()
                    self.log_message("✅ Exception detector initialized successfully")
                except Exception as e:
                    self.log_message(f"❌ Failed to initialize detector: {e}")
            
            def log_message(self, message):
                """Log message to status display"""
                from datetime import datetime
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.status_text.append(f"[{timestamp}] {message}")
            
            def check_health(self):
                """Check system health"""
                if not self.detector:
                    self.log_message("❌ Detector not initialized")
                    return
                
                try:
                    self.log_message("🏥 Checking system health...")
                    health = self.detector.统计分析器.计算健康度评分()
                    
                    # Update health display
                    self.health_score.setText(f"{health.总分:.1f}")
                    self.health_level.setText(health.评级)
                    
                    # Update statistics
                    stats = health.异常统计
                    self.critical_count.setText(str(stats.get("Critical", 0)))
                    self.error_count.setText(str(stats.get("Error", 0)))
                    self.warning_count.setText(str(stats.get("Warning", 0)))
                    self.info_count.setText(str(stats.get("Info", 0)))
                    
                    self.log_message(f"✅ Health check completed: {health.总分:.1f} ({health.评级})")
                    
                    if health.建议:
                        self.log_message("💡 Recommendations:")
                        for suggestion in health.建议[:3]:
                            self.log_message(f"  • {suggestion}")
                    
                except Exception as e:
                    self.log_message(f"❌ Health check failed: {e}")
            
            def generate_report(self):
                """Generate analysis report"""
                if not self.detector:
                    self.log_message("❌ Detector not initialized")
                    return
                
                try:
                    self.log_message("📝 Generating report...")
                    reports = self.detector.生成报告(格式列表=["JSON", "HTML"])
                    
                    if reports:
                        self.log_message("✅ Report generated successfully:")
                        for format_type, file_path in reports.items():
                            self.log_message(f"  📄 {format_type}: {file_path}")
                    else:
                        self.log_message("❌ Failed to generate report")
                        
                except Exception as e:
                    self.log_message(f"❌ Report generation failed: {e}")
            
            def start_monitor(self):
                """Start real-time monitoring"""
                self.log_message("🔍 Real-time monitoring feature")
                self.log_message("💡 Use command line for monitoring: python 异常检测器.py --monitor")
        
        # Create and run application
        app = QApplication(sys.argv)
        app.setApplicationName("Exception Monitor")
        
        window = ExceptionMonitorWindow()
        window.show()
        
        print("✅ GUI window displayed")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return 1
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Main function"""
    print("🎯 Exception Monitor - English GUI Launcher")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Create GUI
    return create_english_gui()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 User interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher failed: {e}")
        sys.exit(1)
